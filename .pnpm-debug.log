{"0 debug pnpm:scope": {"selected": 1}, "1 error pnpm": {"message": {"code": "ERR_PNPM_NO_IMPORTER_MANIFEST_FOUND"}, "err": {"name": "Error", "message": "No package.json (or package.yaml, or package.json5) was found in \"/opt/homebrew/pnpm-global/node_modules/mantas\".", "code": "ERR_PNPM_NO_IMPORTER_MANIFEST_FOUND", "stack": "Error: No package.json (or package.yaml, or package.json5) was found in \"/opt/homebrew/pnpm-global/node_modules/mantas\".\n    at readProjectManifest (/opt/homebrew/pnpm-global/4/node_modules/.pnpm/pnpm@5.18.9/node_modules/pnpm/dist/pnpm.js:43153:11)\n    at async Object.readProjectManifestOnly (/opt/homebrew/pnpm-global/4/node_modules/.pnpm/pnpm@5.18.9/node_modules/pnpm/dist/pnpm.js:43157:24)\n    at async Object.readProjectManifestOnly (/opt/homebrew/pnpm-global/4/node_modules/.pnpm/pnpm@5.18.9/node_modules/pnpm/dist/pnpm.js:43372:22)\n    at async /opt/homebrew/pnpm-global/4/node_modules/.pnpm/pnpm@5.18.9/node_modules/pnpm/dist/pnpm.js:114711:28"}}}