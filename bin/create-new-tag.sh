#!/bin/bash

tagMessage=

# bin文件夹的上一级;当前文件夹的上一级
curPath=$(dirname $(dirname "$0"))
tagFile='tag-info'

while getopts "m:" arg
  do
    case $arg in
      m)
      tagMessage=$OPTARG
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

echo "拉取线上最新的tag列表"
git fetch origin --prune

# 生产表示
echo "正在生成最新的tag"
productionName="beta"
firstVersion="t3"
secondVersion=$(date +%y)
weekNumber=$(date +%V)

maxValue=$(git tag | grep $firstVersion.$secondVersion$weekNumber | grep -v beta | awk -F$firstVersion.$secondVersion$weekNumber. '{print $2}' | awk 'BEGIN {max=-1} {if ($1+0>max+0) max=$1} END {print max}')

let "endTag=maxValue+1"
newTag=$firstVersion.$secondVersion$weekNumber.$endTag
echo "生成成功！"
echo '最新tag为：'$newTag

# 打tag前需要先记录当前的提交备注信息至git中，然后进行提交后再打tag
if [[ $tagMessage != '' ]]; then
  if [ ! -z "$curPath/log" ]; then
    mkdir "log"
  fi

  touch "log/$newTag"
  echo $tagMessage > "log/$newTag"

  git add "log/$newTag"
  git commit -m "chore(log): add $newTag version message"
  git push
fi

git tag $newTag
git push origin --tags

