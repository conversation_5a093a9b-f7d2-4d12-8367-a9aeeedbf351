#!/bin/bash
command=
tagMessage=
curPath=$(dirname "$0")

while getopts "m:" arg
  do
    case $arg in
      m)
      tagMessage=${OPTARG}
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

/bin/sh $curPath/branch/merge-default-branch.sh

if [ $? = 0 ]; then
  echo "正在拉取最新代码"
  git pull
  echo "正在提交最新代码"
  git push
  # 需要记录当前tag所提交的备注
  if [ ! -z $tagMessage ]; then
    $curPath/create-new-tag.sh -m $tagMessage
  else 
    $curPath/create-new-tag.sh
  fi
  # $curPath/create-new-tag.sh -m $tagMessage
else
  echo "代码合并有错误，请处理后，重新执行该命令"
fi

