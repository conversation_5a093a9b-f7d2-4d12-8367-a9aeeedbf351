#!/bin/bash

# submit-test.sh - 配合 submitTest.js 执行 git 操作的脚本
# 参数：mainBranch currentBranch [testBranch]

mainBranch=$1
currentBranch=$2
testBranch=$3

# 参数验证
if [ -z "$mainBranch" ] || [ -z "$currentBranch" ]; then
    echo "错误: 缺少必要参数"
    echo "用法: submit-test.sh <mainBranch> <currentBranch> [testBranch]"
    exit 1
fi

# 如果没有提供测试分支，默认使用当前分支
if [ -z "$testBranch" ]; then
    testBranch=$currentBranch
fi

echo "开始执行 submit-test 操作..."
echo "主分支: $mainBranch"
echo "当前分支: $currentBranch"
echo "测试分支: $testBranch"

# 步骤1: 拉取最新代码
echo "步骤1: 拉取最新代码..."
if ! git pull; then
    echo "错误: git pull 失败"
    exit 1
fi

# 步骤2: 合并主分支最新代码
echo "步骤2: 合并主分支 origin/$mainBranch 的最新代码..."
if ! git merge origin/$mainBranch; then
    echo "错误: 合并主分支失败"
    exit 1
fi

# 步骤3: 切换到测试分支
echo "步骤3: 切换到测试分支 $testBranch..."
if ! git checkout $testBranch; then
    echo "错误: 切换到测试分支失败"
    exit 1
fi

# 步骤4: 将当前分支合并到测试分支
echo "步骤4: 将分支 $currentBranch 合并到测试分支..."
if ! git merge $currentBranch; then
    echo "错误: 合并到测试分支失败"
    exit 1
fi

# 步骤5: 推送到远程
echo "步骤5: 推送到远程仓库..."
if ! git push; then
    echo "错误: 推送失败"
    exit 1
fi

# 步骤6: 切换回当前分支
echo "步骤6: 切换回当前分支 $currentBranch..."
if ! git checkout $currentBranch; then
    echo "错误: 切换回当前分支失败"
    exit 1
fi

echo "submit-test 操作执行成功！"