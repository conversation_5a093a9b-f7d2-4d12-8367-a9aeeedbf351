#!/bin/bash
branchName=
defaultBranch=

# bin文件夹的上一级;当前文件夹的上一级
curPath=$(dirname $(dirname "$0"))

defaultBranch=$($curPath/bin/branch/get-default-branch.sh)

while getopts ":n:d:" arg
  do
    case $arg in
      n)
      branchName=$OPTARG
      ;;
      d)
      defaultBranch=$OPTARG
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

echo "拉取线上最新的branch列表"
git fetch origin --prune

echo "正在获取branch"

if [ -z $branchName ]; then
  # Extract the desired data structure using regular expression matching
  dataStructure=$(git branch | grep -Eo 'd[0-9]+\.[0-9]+\.[0-9]+')


  # 生产表示
  firstVersion="d3"
  secondVersion=$(date +%y)
  weekNumber=$(date +%V)

  # git tag | grep d3.2411 | grep -v beta | awk -Fd3.2411. '{print $2}' | awk 'BEGIN {max=-1} {if ($1+0>max+0) max=$1} END {print max}'
  maxValue=$(git branch -a | grep $firstVersion.$secondVersion$weekNumber | grep -v beta | awk -F$firstVersion.$secondVersion$weekNumber. '{print $2}' | awk 'BEGIN {max=-1} {if ($1+0>max+0) max=$1} END {print max}')

  let "endTag=maxValue+1"
  branchName=$firstVersion.$secondVersion$weekNumber.$endTag
fi

echo "获取成功！"
echo '最新branch为：'$branchName

# 打branch流程
git checkout $defaultBranch
/bin/sh $curPath/bin/branch/merge-default-branch.sh
git checkout -b $branchName
