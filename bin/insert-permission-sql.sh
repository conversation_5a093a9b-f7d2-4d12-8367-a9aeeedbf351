#! /bin/bash

sql=

read -p "请输入要添加的权限：" permission

read -p "请输入添加的菜单名称：" title

read -p "请输入所属平台：" module

read -p "请输入URL：" url

read -p "请输入排序：" sort

read -p "父级权限值：" titlePermission

read -p "icon地址: " icon

read -p "请输入文件夹名称：" branchName

if [ ! -n "$titlePermission" ]; then
  sql="insert into permission (permission,name,description,module,parent_id,url,sort,is_delete,icon) VALUES ('$permission', '$title', '$title','$module', NULL, '$url', '$sort', 0, '$icon');"
else
  sql="insert into permission (permission,name,description,module,parent_id,url,sort,is_delete,icon) select '$permission','$title','$title','$module',id,'$url','$sort',0,'$icon' from \`permission\` where permission='$titlePermission';"
fi

echo "请复制下面的SQL："
echo $sql

# mkdir -p ~/Desktop/版本发布整理/${branchName}
# touch ~/Desktop/版本发布整理/${branchName}/权限.sql
# echo $sql >> ~/Desktop/版本发布整理/${branchName}/权限.sql
exit 0;
