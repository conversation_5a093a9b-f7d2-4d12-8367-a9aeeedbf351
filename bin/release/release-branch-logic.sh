#!/bin/sh

path=
outBranch=

while getopts "p:o:" arg
  do
    case $arg in
      p)
      path=${OPTARG}
      ;;
      o)
      outBranch=${OPTARG}
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

echo "正在打开$path"
cd $path

exitedBranch=$(git branch -a | grep $outBranch)

defaultBranch=$(git branch -r | grep 'HEAD ->' | awk '{print $3}' | awk 'BEGIN{FS="/"} {print $2}')

if [ "$defaultBranch" == "" ]; then
    defaultBranch='master'
fi

echo $defaultBranch

if [ "$exitedBranch" == "" ]; then
  echo "不存在$exitedBranch"
  echo "正在打切分到 master"
  git checkout $defaultBranch
  echo "正在执行mantas merge beta tag"
  mantas merge beta tag
  echo "正在新切分支到$outBranch"
  git checkout -b $outBranch
  echo "$path 新切 $outBranch 分支成功"
else
  echo "$path 已存在$exitedBranch，不会再重新切分支"
  git  checkout $outBranch
fi
