#!/bin/bash

function get_newest_publish_tag() {
  git fetch origin --prune

  productionName="beta"
  tagListStr=$(git tag | grep $productionName | grep ^t)
  node -e "
    let maxPublishVersion;
    try {
      const tagListStr = process.argv[1];
      const productName = process.argv[2];
      const tagList = tagListStr.split('\n');
      if (tagList && tagList.length) {
        const splitedVersions = [];
        tagList.forEach(tag => {
          const versionList = tag.split('.');
          const firstVersion = Number(versionList[0].replace('t', ''));
          const secondVersion = Number(versionList[1]);
          const thirdVersion = Number(versionList[2].replace('-' + productName, ''));
          const splitedVersion = splitedVersions.find(item => item.value === firstVersion);
          if (!splitedVersion) {
            splitedVersions.push({
              value: firstVersion,
              children: [{
                value: secondVersion,
                children: [{
                  value: thirdVersion,
                }]
              }],
            });
            return ;
          }
          const splitedSecondVersion = splitedVersion.children.find(item => item.value === secondVersion);
          if (!splitedSecondVersion) {
            splitedVersion.children.push({
              value: secondVersion,
              children: [{
                value: thirdVersion,
              }]
            });
            return ;
          }
          const splitedThirdVersion = splitedSecondVersion.children.find(item => item.value === thirdVersion);
          if (!splitedThirdVersion) {
            splitedSecondVersion.children.push({
              value: thirdVersion,
            })
          }
        });
        splitedVersions.map(version => version.children.map(item => item.children.sort((a, b) => a.value - b.value)).sort((a, b) => a.value - b.value)).sort((a, b) => a.value - b.value);

        const maxFirstVersion = splitedVersions[splitedVersions.length - 1].value;
        const maxFirstVersionChildren = splitedVersions[splitedVersions.length - 1].children;
        const maxSecondVersion = maxFirstVersionChildren[maxFirstVersionChildren.length - 1].value;
        const maxSecondVersionChildren = maxFirstVersionChildren[maxFirstVersionChildren.length - 1].children;
        const maxThridVersion = maxSecondVersionChildren[maxSecondVersionChildren.length - 1].value;
        maxPublishVersion = ['t' + maxFirstVersion, maxSecondVersion, maxThridVersion + '-' + productName].join('.');
      } else {
        maxPublishVersion = 'master';
      }
    } catch (e) {
    } finally {
      console.log(maxPublishVersion || '没有beta tag');
    }
  " "$tagListStr" "$productionName"
}

publishedTag=$(get_newest_publish_tag)

echo $publishedTag
