#!/bin/sh
# 1. 先找出有哪些文件是属于新增的，比如不存在与master上的改动过的文件
# 1.1 确定当前分支/tag
tagBranch=$1
originTagBranch=

remoteBranch=$(git branch -a | grep origin/$tagBranch)

if [ -z $remoteBranch ]; then
  originTagBranch=$tagBranch
else
  originTagBranch=origin/$tagBranch
fi

# 1.2 找出default分支
curPath=$(dirname $(dirname "$0"))
defaultBranch=$($curPath/branch/get-default-branch.sh);

# 1.3 同步远程master的提交信息，但不拉取到本地，只做远程信息更新
git fetch origin $defaultBranch

# echo $defaultBranch
# 1.4 以default分支为基准值，找出差异的commit的文件信息
# echo "git log $originTagBranch ^origin/master --pretty=format:"" --name-only | sort | uniq"
files=$(git log $originTagBranch ^origin/$defaultBranch --pretty=format:"" --name-only | sort | uniq)

echo $files;
