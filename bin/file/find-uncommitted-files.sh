
files=

# tracked
tracked=$(git diff --pretty=format: --name-only)

# untracked
untracked=$(git ls-files -o --exclude-standard)

while getopts "tuU" arg
  do
    case $arg in
        t)
        # tracked
        files="$tracked"
        ;;
        u)
        files="$untracked"
        ;;
        U)
        files="$untracked$tracked"
        ;;
        ?)
        echo "未知参数: $arg"
        exit 1
        ;;
  esac
done

echo $files
