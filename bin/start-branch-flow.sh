#!/bin/bash
branchName=
path=
curPath=$(dirname "$0")

while getopts "n:p:" arg
  do
    case $arg in
      n)
      branchName=${OPTARG}
      ;;
      p)
      path=${OPTARG}
      ;;
      ?)
      echo "未知参数: $arg"
      exit 1
      ;;
  esac
done

# shell中判断不为空
if [ -n "$path" ]; then
  # path are not empty
  cd $path
fi

# if [ $? = 0 ]; then

# else
#   echo "代码合并有错误，请处理后，重新执行该命令"
# fi

# 需要记录当前tag所提交的备注
if [ ! -z $branchName ]; then
  $curPath/create-new-branch.sh -n $branchName
else
  $curPath/create-new-branch.sh
fi
