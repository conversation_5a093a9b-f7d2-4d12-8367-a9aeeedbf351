#!/bin/sh

path=$1
name=$2

# 进入项目
cd $path

# echo "拉取线上最新的tag列表"
git fetch origin --prune

# 生产表示
# echo "正在生成最新的tag"
productionName="beta"
firstVersion="t3"
secondVersion=$(date +%y)
weekNumber=$(date +%V)

maxValue=$(git tag | grep $firstVersion.$secondVersion$weekNumber | grep -v beta | awk -F$firstVersion.$secondVersion$weekNumber. '{print $2}' | awk 'BEGIN {max=-1} {if ($1+0>max+0) max=$1} END {print max}')

let "endTag=maxValue+1"
newTag=$firstVersion.$secondVersion$weekNumber.$endTag
echo $newTag