#!/bin/sh

# bin文件夹的上一级;当前文件夹的上一级
curPath=$(dirname $(dirname $(dirname "$0")))
tagFile="tag-info"
tagFilePath="$curPath/$tagFile"

# 生产表示
echo "正在生成最新的tag"
productionName="beta"
firstVersion="t3"

# 第二位组成
yearVersion=$(date +%y)
weekNumber=$(date +%V)
secondVersion=$yearVersion$weekNumber

# 第三位组成
monthNumber=$(date +%b)
dayNumber=$(date +%d)
hourNumber=$(date +%H)
minuteNumber=$(date +%M)
endVersion=$monthNumber$dayNumber$hourNumber$minuteNumber

newTag=$firstVersion.$secondVersion.$endVersion

echo "tag生成成功！"
echo $newTag

if [ -d $tagFilePath ]; then
    rm `$curPath/$tagFile`
fi
cd $curPath
touch $tagFile
echo $newTag > $tagFile
