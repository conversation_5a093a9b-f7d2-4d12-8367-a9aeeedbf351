lockfileVersion: 5.3

specifiers:
  '@mantas/request': ^1.0.10
  art-template: ^4.13.2
  chalk: ^4.1.0
  commander: ^6.0.0
  dotenv: ^16.0.1
  husky: ^4.2.5
  inquirer: ^7.3.3
  miniprogram-ci: ^1.8.35
  ora: ^5.0.0

dependencies:
  '@mantas/request': 1.0.12
  art-template: registry.npmmirror.com/art-template/4.13.2
  chalk: 4.1.2
  commander: 6.2.1
  dotenv: 16.0.1
  inquirer: 7.3.3
  miniprogram-ci: 1.8.35
  ora: 5.4.1

devDependencies:
  husky: 4.3.8

packages:

  /@ampproject/remapping/2.1.2:
    resolution: {integrity: sha512-hoyByceqwKirw7w3Z7gnIIZC3Wx3J484Y3L/cMpXFbr7d9ZQj2mODrirNzcJa+SM3UlpWXYvKV4RlRpFXlWgXg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/trace-mapping': 0.3.4
    dev: false

  /@babel/code-frame/7.16.7:
    resolution: {integrity: sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.16.10

  /@babel/code-frame/7.18.6:
    resolution: {integrity: sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.18.6
    dev: false

  /@babel/compat-data/7.17.0:
    resolution: {integrity: sha512-392byTlpGWXMv4FbyWw3sAZ/FrW/DrwqLGXpy0mbyNe9Taqv1mg9yON5/o0cnr8XYCkFTZbC1eV+c+LAROgrng==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/compat-data/7.18.8:
    resolution: {integrity: sha512-HSmX4WZPPK3FUxYp7g2T6EyO8j96HlZJlxmKPSh6KAcqwyDrfx7hKjXpAW/0FhFfTJsR0Yt4lAjLI2coMptIHQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/core/7.12.3:
    resolution: {integrity: sha512-0qXcZYKZp3/6N2jKYVxZv0aNCsxTSVCiK72DTiTYZAu7sjg73W0/aynWjMbiGd87EQL4WyA8reiJVh92AVla9g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@babel/generator': 7.17.10
      '@babel/helper-module-transforms': 7.17.6
      '@babel/helpers': 7.17.2
      '@babel/parser': 7.17.10
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.10
      '@babel/types': 7.17.0
      convert-source-map: 1.8.0
      debug: 4.3.3
      gensync: 1.0.0-beta.2
      json5: 2.2.0
      lodash: 4.17.21
      resolve: 1.22.0
      semver: 5.7.1
      source-map: registry.npmmirror.com/source-map/0.5.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/core/7.17.5:
    resolution: {integrity: sha512-/BBMw4EvjmyquN5O+t5eh0+YqB3XXJkYD2cjKpYtWOfFy4lQ4UozNSmxAcWT8r2XtZs0ewG+zrfsqeR15i1ajA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.1.2
      '@babel/code-frame': 7.16.7
      '@babel/generator': 7.17.3
      '@babel/helper-compilation-targets': 7.16.7_@babel+core@7.17.5
      '@babel/helper-module-transforms': 7.17.6
      '@babel/helpers': 7.17.2
      '@babel/parser': 7.17.3
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.3
      '@babel/types': 7.17.0
      convert-source-map: 1.8.0
      debug: 4.3.3
      gensync: 1.0.0-beta.2
      json5: 2.2.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/generator/7.17.10:
    resolution: {integrity: sha512-46MJZZo9y3o4kmhBVc7zW7i8dtR1oIK/sdO5NcfcZRhTGYi+KKJRtHNgsU6c4VUcJmUNV/LQdebD/9Dlv4K+Tg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
      '@jridgewell/gen-mapping': 0.1.1
      jsesc: 2.5.2
    dev: false

  /@babel/generator/7.17.3:
    resolution: {integrity: sha512-+R6Dctil/MgUsZsZAkYgK+ADNSZzJRRy0TvY65T71z/CR854xHQ1EweBYXdfT+HNeN7w0cSJJEzgxZMv40pxsg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
      jsesc: 2.5.2
      source-map: registry.npmmirror.com/source-map/0.5.7
    dev: false

  /@babel/generator/7.18.10:
    resolution: {integrity: sha512-0+sW7e3HjQbiHbj1NeU/vN8ornohYlacAfZIaXhdoGweQqgcNy69COVciYYqEXJ/v+9OBA7Frxm4CVAuNqKeNA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
      '@jridgewell/gen-mapping': 0.3.2
      jsesc: 2.5.2
    dev: false

  /@babel/helper-annotate-as-pure/7.18.6:
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-builder-binary-assignment-operator-visitor/7.18.9:
    resolution: {integrity: sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-explode-assignable-expression': 7.18.6
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-compilation-targets/7.16.7_@babel+core@7.12.3:
    resolution: {integrity: sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.17.0
      '@babel/core': 7.12.3
      '@babel/helper-validator-option': 7.16.7
      browserslist: 4.19.3
      semver: 6.3.0
    dev: false

  /@babel/helper-compilation-targets/7.16.7_@babel+core@7.17.5:
    resolution: {integrity: sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.17.0
      '@babel/core': 7.17.5
      '@babel/helper-validator-option': 7.16.7
      browserslist: 4.19.3
      semver: 6.3.0
    dev: false

  /@babel/helper-compilation-targets/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-tzLCyVmqUiFlcFoAPLA/gL9TeYrF61VLNtb+hvkuVaB5SUjW7jcfrglBIX1vUIoT7CLP3bBlIMeyEsIl2eFQNg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.18.8
      '@babel/core': 7.12.3
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.3
      semver: 6.3.0
    dev: false

  /@babel/helper-create-class-features-plugin/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-WvypNAYaVh23QcjpMR24CwZY2Nz6hqdOcFdPbNpV56hL5H6KiFheO7Xm1aPdlLQ7d5emYZX7VZwPp9x3z+2opw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.18.9
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-create-regexp-features-plugin/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-7LcpH1wnQLGrI+4v+nPp+zUvIkF9x0ddv1Hkdue10tg3gmRnLy97DXh4STiOf1qeIInyD69Qv5kKSZzKD8B/7A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-annotate-as-pure': 7.18.6
      regexpu-core: 5.1.0
    dev: false

  /@babel/helper-environment-visitor/7.16.7:
    resolution: {integrity: sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-environment-visitor/7.18.9:
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-explode-assignable-expression/7.18.6:
    resolution: {integrity: sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-function-name/7.16.7:
    resolution: {integrity: sha512-QfDfEnIUyyBSR3HtrtGECuZ6DAyCkYFp7GHl75vFtTnn6pjKeK0T1DB5lLkFvBea8MdaiUABx3osbgLyInoejA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-get-function-arity': 7.16.7
      '@babel/template': 7.16.7
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-function-name/7.18.9:
    resolution: {integrity: sha512-fJgWlZt7nxGksJS9a0XdSaI4XvpExnNIgRP+rVefWh5U7BL8pPuir6SJUmFKRfjWQ51OtWSzwOxhaH/EBWWc0A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-get-function-arity/7.16.7:
    resolution: {integrity: sha512-flc+RLSOBXzNzVhcLu6ujeHUrD6tANAOU5ojrRx/as+tbzf8+stUCj7+IfRRoAbEZqj/ahXEMsjhOhgeZsrnTw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-hoist-variables/7.16.7:
    resolution: {integrity: sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-hoist-variables/7.18.6:
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-member-expression-to-functions/7.18.9:
    resolution: {integrity: sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-module-imports/7.12.1:
    resolution: {integrity: sha512-ZeC1TlMSvikvJNy1v/wPIazCu3NdOwgYZLIkmIyAsGhqkNpiDoQQRmaCK8YP4Pq3GPTLPV9WXaPCJKvx06JxKA==}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-module-imports/7.16.7:
    resolution: {integrity: sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-module-imports/7.18.6:
    resolution: {integrity: sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-module-transforms/7.17.6:
    resolution: {integrity: sha512-2ULmRdqoOMpdvkbT8jONrZML/XALfzxlb052bldftkicAUy8AxSCkD5trDPQcwHNmolcl7wP6ehNqMlyUw6AaA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.16.7
      '@babel/helper-module-imports': 7.16.7
      '@babel/helper-simple-access': 7.16.7
      '@babel/helper-split-export-declaration': 7.16.7
      '@babel/helper-validator-identifier': 7.16.7
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.3
      '@babel/types': 7.17.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-module-transforms/7.18.9:
    resolution: {integrity: sha512-KYNqY0ICwfv19b31XzvmI/mfcylOzbLtowkw+mfvGPAQ3kfCnMLYbED3YecL5tPd8nAYFQFAd6JHp2LxZk/J1g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-simple-access': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.18.6
      '@babel/template': 7.18.10
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-optimise-call-expression/7.18.6:
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-plugin-utils/7.16.7:
    resolution: {integrity: sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-plugin-utils/7.18.9:
    resolution: {integrity: sha512-aBXPT3bmtLryXaoJLyYPXPlSD4p1ld9aYeR+sJNOZjJJGiOpb+fKfh3NkcCu7J54nUJwCERPBExCCpyCOHnu/w==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-remap-async-to-generator/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-wrap-function': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-replace-supers/7.18.9:
    resolution: {integrity: sha512-dNsWibVI4lNT6HiuOIBr1oyxo40HvIVmbwPUm3XZ7wMh4k2WxrxTqZwSqw/eEmXDS9np0ey5M2bz9tBmO9c+YQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-simple-access/7.16.7:
    resolution: {integrity: sha512-ZIzHVyoeLMvXMN/vok/a4LWRy8G2v205mNP0XOuf9XRLyX5/u9CnVulUtDgUTama3lT+bf/UqucuZjqiGuTS1g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-simple-access/7.18.6:
    resolution: {integrity: sha512-iNpIgTgyAvDQpDj76POqg+YEt8fPxx3yaNBg3S30dxNKm2SWfYhD0TGrK/Eu9wHpUW63VQU894TsTg+GLbUa1g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-skip-transparent-expression-wrappers/7.18.9:
    resolution: {integrity: sha512-imytd2gHi3cJPsybLRbmFrF7u5BIEuI2cNheyKi3/iOBC63kNn3q8Crn2xVuESli0aM4KYsyEqKyS7lFL8YVtw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-split-export-declaration/7.16.7:
    resolution: {integrity: sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/helper-split-export-declaration/7.18.6:
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/helper-string-parser/7.18.10:
    resolution: {integrity: sha512-XtIfWmeNY3i4t7t4D2t02q50HvqHybPqW2ki1kosnvWCwuCMeo81Jf0gwr85jy/neUdg5XDdeFE/80DXiO+njw==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-identifier/7.16.7:
    resolution: {integrity: sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier/7.18.6:
    resolution: {integrity: sha512-MmetCkz9ej86nJQV+sFCxoGGrUbU3q02kgLciwkrt9QqEB7cP39oKEY0PakknEO0Gu20SskMRi+AYZ3b1TpN9g==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-option/7.16.7:
    resolution: {integrity: sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-option/7.18.6:
    resolution: {integrity: sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-wrap-function/7.18.11:
    resolution: {integrity: sha512-oBUlbv+rjZLh2Ks9SKi4aL7eKaAXBWleHzU89mP0G6BMUlRxSckk9tSIkgDGydhgFxHuGSlBQZfnaD47oBEB7w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-function-name': 7.18.9
      '@babel/template': 7.18.10
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helpers/7.12.1:
    resolution: {integrity: sha512-9JoDSBGoWtmbay98efmT2+mySkwjzeFeAL9BuWNoVQpkPFQF8SIIFUfY5os9u8wVzglzoiPRSW7cuJmBDUt43g==}
    dependencies:
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.10
      '@babel/types': 7.18.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helpers/7.17.2:
    resolution: {integrity: sha512-0Qu7RLR1dILozr/6M0xgj+DFPmi6Bnulgm9M8BVa9ZCWxDqlSnqt3cf8IDPB5m45sVXUZ0kuQAgUrdSFFH79fQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.3
      '@babel/types': 7.17.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/highlight/7.16.10:
    resolution: {integrity: sha512-5FnTQLSLswEj6IkgVw5KusNUUFY9ZGqe/TRFnP/BKYHYgfh7tc+C7mwiy95/yNP7Dh9x580Vv8r7u7ZfTBFxdw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.16.7
      chalk: 2.4.2
      js-tokens: registry.npmmirror.com/js-tokens/4.0.0

  /@babel/highlight/7.18.6:
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.18.6
      chalk: 2.4.2
      js-tokens: registry.npmmirror.com/js-tokens/4.0.0
    dev: false

  /@babel/parser/7.17.10:
    resolution: {integrity: sha512-n2Q6i+fnJqzOaq2VkdXxy2TCPCWQZHiCo0XqmrCvDWcZQKRyZzYi4Z0yxlBuN0w+r2ZHmre+Q087DSrw3pbJDQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/parser/7.17.3:
    resolution: {integrity: sha512-7yJPvPV+ESz2IUTPbOL+YkIGyCqOyNIzdguKQuJGnH7bg1WTIifuM21YqokFt/THWh1AkCRn9IgoykTRCBVpzA==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.17.0
    dev: false

  /@babel/parser/7.18.11:
    resolution: {integrity: sha512-9JKn5vN+hDt0Hdqn1PiJ2guflwP+B6Ga8qbDuoF0PzzVhrzsKIJo8yGqVk6CmMHiMei9w1C1Bp9IMJSIK+HPIQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@babel/plugin-proposal-async-generator-functions/7.18.10_@babel+core@7.12.3:
    resolution: {integrity: sha512-1mFuY2TOsR1hxbjCo4QL+qlIjV07p4H4EUYw2J/WCqsvFV6V9X9z9YhXbWndc/4fw+hYGlDT7egYxliMp5O6Ew==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-remap-async-to-generator': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.12.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-class-properties/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-cKp3dlQsFsEs5CWKnN7BnSHOd0EOW8EKpEjkoz1pO2E5KzIDNV9Ros1b0CnmbVgAGXJubOYVBOGCT1OmJwOI7w==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-class-features-plugin': 7.18.9_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.16.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-decorators/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-knNIuusychgYN8fGJHONL0RbFxLGawhXOJNLBk75TniTsZZeA+wdkDuv6wp4lGwzQEKjZi6/WYtnb3udNPmQmQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-class-features-plugin': 7.18.9_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/plugin-syntax-decorators': 7.18.6_@babel+core@7.12.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-do-expressions/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-bpJ6Bfrzvdzb0vG6zBSNh3HLgFKh+S2CBpNmaLRjg2u7cNkzRPIqBjVURCmpG6pvPfKyxkizwbrXwpYtW3a9cw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/plugin-syntax-do-expressions': 7.18.6_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-dynamic-import/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-export-default-from/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-z5Q4Ke7j0AexQRfgUvnD+BdCSgpTEKnqQ3kskk2jWtOBulxICzd1X9BGt7kmWftxZ2W3++OZdt5gtmC8KLxdRQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/plugin-syntax-export-default-from': 7.18.6_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-export-namespace-from/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-export-namespace-from': 7.8.3_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-function-bind/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-Nic0blOXoeyuDJZJNh7kEZMqQUHakiUyxfyFMUV0Sy7DQ+Du9R7cZCUgTLnqq7Bc0Yx0iKRSe5wTmRWLKwxxpA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/plugin-syntax-function-bind': 7.18.6_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-function-sent/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-EXB01ACyNW0WCffP4ip40TH82X86+U0dakFZjyiMpoZ8NFmL5MMARzVBzy+Gg59B6vTgfvIhRHUhe6tNUw+vjw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/helper-wrap-function': 7.18.11
      '@babel/plugin-syntax-function-sent': 7.18.6_@babel+core@7.12.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-json-strings/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-logical-assignment-operators/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-128YbMpjCrP35IOExw2Fq+x55LMP42DzhOhX2aNNIdI9avSWl2PI0yuBWarr3RYpZBSPtabfadkH2yeRiMD61Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-nullish-coalescing-operator/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-numeric-separator/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-object-rest-spread/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-kDDHQ5rflIeY5xl69CEqGEZ0KY369ehsCIEbTGb4siHG5BE9sga/T0r0OUwyZNLMmZE79E1kbsqAjwFCW4ds6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.18.8
      '@babel/core': 7.12.3
      '@babel/helper-compilation-targets': 7.18.9_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-transform-parameters': 7.18.8_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-optional-catch-binding/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-optional-chaining/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-skip-transparent-expression-wrappers': 7.18.9
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-pipeline-operator/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-iloNp4xu8YV8e/mZgGjePg9be1VkJSxQWIplRwgQtQPtF26ar3cHXL4sV8Fujlm2mm/Tu/WiA+FU+Fp7QVP7/g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/plugin-syntax-pipeline-operator': 7.18.6_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-private-methods/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-mwZ1phvH7/NHK6Kf8LP7MYDogGV+DKB1mryFOEwx5EBNQrosvIczzZFTUmWaeujd5xT6G1ELYWUz3CutMhjE1w==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-class-features-plugin': 7.18.9_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.16.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-throw-expressions/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-kiWkKtm05K86C+T/nUazv+/Vxu93Aulrvof/ZrxVyGoUBVsVEWDrw9iChbe8tV+aPVQcjg4FQxKW3wUF7cRcpg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/plugin-syntax-throw-expressions': 7.18.6_@babel+core@7.12.3
    dev: false

  /@babel/plugin-proposal-unicode-property-regex/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-regexp-features-plugin': 7.18.6_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.12.3:
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.17.5:
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-bigint/7.8.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-class-properties/7.12.13_@babel+core@7.12.3:
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-class-properties/7.12.13_@babel+core@7.17.5:
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-decorators/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-fqyLgjcxf/1yhyZ6A+yo1u9gJ7eleFQod2lkaUsF9DQ7sbbY3Ligym3L0+I2c0WmqNKDpoD9UTb1AKP3qRMOAQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-do-expressions/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-kTogvOsjBTVOSZtkkziiXB5hwGXqwhq2gBXDaiWVruRLDT7C2GqfbsMnicHJ7ePq2GE8UJeWS34YbNP6yDhwUA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-dynamic-import/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-export-default-from/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-Kr//z3ujSVNx6E9z9ih5xXXMqK07VVTuqPmqGe6Mss/zW5XPeLZeSDZoP9ab/hT4wPKqAgjl2PnhPrcpk8Seew==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-export-namespace-from/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-function-bind/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-wZN0Aq/AScknI9mKGcR3TpHdASMufFGaeJgc1rhPmLtZ/PniwjePSh8cfh8tXMB3U4kh/3cRKrLjDtedejg8jQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-function-sent/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-f3OJHIlFIkg+cP1Hfo2SInLhsg0pz2Ikmgo7jMdIIKC+3jVXQlHB0bgSapOWxeWI0SU28qIWmfn5ZKu1yPJHkg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-import-meta/7.10.4_@babel+core@7.17.5:
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4_@babel+core@7.12.3:
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4_@babel+core@7.17.5:
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.12.3:
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.17.5:
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.12.3:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-pipeline-operator/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-pFtIdQomJtkTHWcNsGXhjJ5YUkL+AxJnP4G+Ol85UO6uT2fpHTPYLLE5bBeRA9cxf25qa/VKsJ3Fi67Gyqe3rA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-throw-expressions/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-rp1CqEZXGv1z1YZ3qYffBH3rhnOxrTwQG8fh2yqulTurwv9zu3Gthfd+niZBLSOi1rY6146TgF+JmVeDXaX4TQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-top-level-await/7.14.5_@babel+core@7.12.3:
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-top-level-await/7.14.5_@babel+core@7.17.5:
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-typescript/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-syntax-typescript/7.18.6_@babel+core@7.17.5:
    resolution: {integrity: sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-arrow-functions/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-async-to-generator/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-remap-async-to-generator': 7.18.9_@babel+core@7.12.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-block-scoped-functions/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-block-scoping/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-5sDIJRV1KtQVEbt/EIBwGy4T01uYIo4KRB3VUqzkhrAIOGx7AoctL9+Ux88btY0zXdDyPJ9mW+bg+v+XEkGmtw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-classes/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-EkRQxsxoytpTlKJmSPYrsOMjCILacAjtSVkd4gChEe2kXjFCun3yohhW5I7plXJhCemM0gKsaGMcO8tinvCA5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-replace-supers': 7.18.9
      '@babel/helper-split-export-declaration': 7.18.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-computed-properties/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-destructuring/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-p5VCYNddPLkZTq4XymQIaIfZNJwT9YsjkPOhkVEqt6QIpQFZVM9IltqqYpOEkJoN1DPznmxUDyZ5CTZs/ZCuHA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-dotall-regex/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-regexp-features-plugin': 7.18.6_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-duplicate-keys/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-exponentiation-operator/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-for-of/7.18.8_@babel+core@7.12.3:
    resolution: {integrity: sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-function-name/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-compilation-targets': 7.18.9_@babel+core@7.12.3
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-literals/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-member-expression-literals/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-modules-amd/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-Pra5aXsmTsOnjM3IajS8rTaLCy++nGM4v3YR4esk5PCsyg9z8NA5oQLwxzMUtDBd8F+UmVza3VxoAaWCbzH1rg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-module-transforms': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
      babel-plugin-dynamic-import-node: 2.3.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-commonjs/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-Qfv2ZOWikpvmedXQJDSbxNqy7Xr/j2Y8/KfijM0iJyKkBTmWuvCA1yeH1yDM7NJhBW/2aXxeucLj6i80/LAJ/Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-module-transforms': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-simple-access': 7.18.6
      babel-plugin-dynamic-import-node: 2.3.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-systemjs/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-zY/VSIbbqtoRoJKo2cDTewL364jSlZGvn0LKOf9ntbfxOvjfmyrdtEEOAdswOswhZEb8UH3jDkCKHd1sPgsS0A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-module-transforms': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-validator-identifier': 7.18.6
      babel-plugin-dynamic-import-node: 2.3.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-umd/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-module-transforms': 7.18.9
      '@babel/helper-plugin-utils': 7.18.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-named-capturing-groups-regex/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-UmEOGF8XgaIqD74bC8g7iV3RYj8lMf0Bw7NJzvnS9qQhM4mg+1WHKotUIdjxgD2RGrgFLZZPCFPFj3P/kVDYhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-regexp-features-plugin': 7.18.6_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-new-target/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-object-super/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-replace-supers': 7.18.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-parameters/7.18.8_@babel+core@7.12.3:
    resolution: {integrity: sha512-ivfbE3X2Ss+Fj8nnXvKJS6sjRG4gzwPMsP+taZC+ZzEGjAYlvENixmt1sZ5Ca6tWls+BlKSGKPJ6OOXvXCbkFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-property-literals/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-regenerator/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      regenerator-transform: 0.15.0
    dev: false

  /@babel/plugin-transform-reserved-words/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-runtime/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-Ac/H6G9FEIkS2tXsZjL4RAdS3L3WHxci0usAnz7laPWUmFiGtj7tIASChqKZMHTSQTQY6xDbOq+V1/vIq3QrWg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-module-imports': 7.16.7
      '@babel/helper-plugin-utils': 7.16.7
      resolve: 1.22.0
      semver: 5.7.1
    dev: false

  /@babel/plugin-transform-shorthand-properties/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-spread/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-39Q814wyoOPtIB/qGopNIL9xDChOE1pNU0ZY5dO0owhiVt/5kFm4li+/bBtwc7QotG0u5EPzqhZdjMtmqBqyQA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/helper-skip-transparent-expression-wrappers': 7.18.9
    dev: false

  /@babel/plugin-transform-sticky-regex/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-template-literals/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-typeof-symbol/7.18.9_@babel+core@7.12.3:
    resolution: {integrity: sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-typescript/7.18.10_@babel+core@7.12.3:
    resolution: {integrity: sha512-j2HQCJuMbi88QftIb5zlRu3c7PU+sXNnscqsrjqegoGiCgXR569pEdben9vly5QHKL2ilYkfnSwu64zsZo/VYQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-class-features-plugin': 7.18.9_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-syntax-typescript': 7.18.6_@babel+core@7.12.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-unicode-escapes/7.18.10_@babel+core@7.12.3:
    resolution: {integrity: sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/plugin-transform-unicode-regex/7.18.6_@babel+core@7.12.3:
    resolution: {integrity: sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-create-regexp-features-plugin': 7.18.6_@babel+core@7.12.3
      '@babel/helper-plugin-utils': 7.18.9
    dev: false

  /@babel/preset-env/7.12.1_@babel+core@7.12.3:
    resolution: {integrity: sha512-H8kxXmtPaAGT7TyBvSSkoSTUK6RHh61So05SyEbpmr0MCZrsNYn7mGMzzeYoOUCdHzww61k8XBft2TaES+xPLg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.17.0
      '@babel/core': 7.12.3
      '@babel/helper-compilation-targets': 7.16.7_@babel+core@7.12.3
      '@babel/helper-module-imports': 7.16.7
      '@babel/helper-plugin-utils': 7.16.7
      '@babel/helper-validator-option': 7.16.7
      '@babel/plugin-proposal-async-generator-functions': 7.18.10_@babel+core@7.12.3
      '@babel/plugin-proposal-class-properties': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-dynamic-import': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-proposal-export-namespace-from': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-proposal-json-strings': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-proposal-logical-assignment-operators': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-proposal-numeric-separator': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-proposal-object-rest-spread': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-proposal-optional-chaining': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-proposal-private-methods': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.12.3
      '@babel/plugin-syntax-class-properties': 7.12.13_@babel+core@7.12.3
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-export-namespace-from': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.12.3
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.12.3
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.12.3
      '@babel/plugin-syntax-top-level-await': 7.14.5_@babel+core@7.12.3
      '@babel/plugin-transform-arrow-functions': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-async-to-generator': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-block-scoped-functions': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-block-scoping': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-classes': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-computed-properties': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-destructuring': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-dotall-regex': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-duplicate-keys': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-exponentiation-operator': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-for-of': 7.18.8_@babel+core@7.12.3
      '@babel/plugin-transform-function-name': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-literals': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-member-expression-literals': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-modules-amd': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-modules-commonjs': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-modules-systemjs': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-modules-umd': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-named-capturing-groups-regex': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-new-target': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-object-super': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-parameters': 7.18.8_@babel+core@7.12.3
      '@babel/plugin-transform-property-literals': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-regenerator': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-reserved-words': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-shorthand-properties': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-spread': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-sticky-regex': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-template-literals': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-typeof-symbol': 7.18.9_@babel+core@7.12.3
      '@babel/plugin-transform-unicode-escapes': 7.18.10_@babel+core@7.12.3
      '@babel/plugin-transform-unicode-regex': 7.18.6_@babel+core@7.12.3
      '@babel/preset-modules': 0.1.5_@babel+core@7.12.3
      '@babel/types': 7.18.10
      core-js-compat: 3.24.1
      semver: 5.7.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/preset-modules/0.1.5_@babel+core@7.12.3:
    resolution: {integrity: sha1-75Odbn8miCfhhBY43G/5VRXhFdk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.12.3
      '@babel/helper-plugin-utils': 7.18.9
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-dotall-regex': 7.18.6_@babel+core@7.12.3
      '@babel/types': 7.18.10
      esutils: registry.npmmirror.com/esutils/2.0.3
    dev: false

  /@babel/runtime/7.12.1:
    resolution: {integrity: sha512-J5AIf3vPj3UwXaAzb5j1xM4WAQDX3EMgemF8rjCP3SoW09LfRKAXQKt6CoVYl230P6iWdRcBbnLDDdnqWxZSCA==}
    dependencies:
      regenerator-runtime: 0.13.9
    dev: false

  /@babel/runtime/7.17.2:
    resolution: {integrity: sha512-hzeyJyMA1YGdJTuWU0e/j4wKXrU4OMFvY2MSlaI9B7VQb0r5cxTE3EAIS2Q7Tn2RIcDkRvTA/v2JsAEhxe99uw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.13.9
    dev: false

  /@babel/template/7.16.7:
    resolution: {integrity: sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@babel/parser': 7.17.10
      '@babel/types': 7.17.0
    dev: false

  /@babel/template/7.18.10:
    resolution: {integrity: sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.18.11
      '@babel/types': 7.18.10
    dev: false

  /@babel/traverse/7.17.10:
    resolution: {integrity: sha512-VmbrTHQteIdUUQNTb+zE12SHS/xQVIShmBPhlNP12hD5poF2pbITW1Z4172d03HegaQWhLffdkRJYtAzp0AGcw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@babel/generator': 7.17.10
      '@babel/helper-environment-visitor': 7.16.7
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-hoist-variables': 7.16.7
      '@babel/helper-split-export-declaration': 7.16.7
      '@babel/parser': 7.17.10
      '@babel/types': 7.18.10
      debug: 4.3.3
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/traverse/7.17.3:
    resolution: {integrity: sha512-5irClVky7TxRWIRtxlh2WPUUOLhcPN06AGgaQSB8AEwuyEBgJVuJ5imdHm5zxk8w0QS5T+tDfnDxAlhWjpb7cw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.16.7
      '@babel/generator': 7.17.3
      '@babel/helper-environment-visitor': 7.16.7
      '@babel/helper-function-name': 7.16.7
      '@babel/helper-hoist-variables': 7.16.7
      '@babel/helper-split-export-declaration': 7.16.7
      '@babel/parser': 7.17.3
      '@babel/types': 7.17.0
      debug: 4.3.3
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/traverse/7.18.11:
    resolution: {integrity: sha512-TG9PiM2R/cWCAy6BPJKeHzNbu4lPzOSZpeMfeNErskGpTJx6trEvFaVCbDvpcxwy49BKWmEPwiW8mrysNiDvIQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.18.10
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.18.9
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.18.11
      '@babel/types': 7.18.10
      debug: 4.3.3
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/types/7.17.0:
    resolution: {integrity: sha512-TmKSNO4D5rzhL5bjWFcVHHLETzfQ/AmbKpKPOSjlP0WoHZ6L911fgoOKY4Alp/emzG4cHJdyN49zpgkbXFEHHw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.16.7
      to-fast-properties: 2.0.0
    dev: false

  /@babel/types/7.18.10:
    resolution: {integrity: sha512-MJvnbEiiNkpjo+LknnmRrqbY1GPUUggjv+wQVjetM/AONoupqRALB7I6jGqNUAZsKcRIEu2J6FRFvsczljjsaQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.18.10
      '@babel/helper-validator-identifier': 7.18.6
      to-fast-properties: 2.0.0
    dev: false

  /@bcoe/v8-coverage/0.2.3:
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==}
    dev: false

  /@istanbuljs/load-nyc-config/1.1.0:
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0
    dev: false

  /@istanbuljs/schema/0.1.3:
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}
    dev: false

  /@jest/console/28.1.3:
    resolution: {integrity: sha512-QPAkP5EwKdK/bxIr6C1I4Vs0rm2nHiANzj/Z5X2JQkrZo6IqvC4ldZ9K95tF0HdidhA8Bo6egxSzUFPYKcEXLw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      chalk: 4.1.2
      jest-message-util: 28.1.3
      jest-util: 28.1.3
      slash: 3.0.0
    dev: false

  /@jest/core/28.1.3:
    resolution: {integrity: sha512-CIKBrlaKOzA7YG19BEqCw3SLIsEwjZkeJzf5bdooVnW4bH5cktqe3JX+G2YV1aK5vP8N9na1IGWFzYaTp6k6NA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/console': 28.1.3
      '@jest/reporters': 28.1.3
      '@jest/test-result': 28.1.3
      '@jest/transform': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.7.0
      exit: 0.1.2
      graceful-fs: 4.2.9
      jest-changed-files: 28.1.3
      jest-config: 28.1.3_@types+node@18.11.17
      jest-haste-map: 28.1.3
      jest-message-util: 28.1.3
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.3
      jest-resolve-dependencies: 28.1.3
      jest-runner: 28.1.3
      jest-runtime: 28.1.3
      jest-snapshot: 28.1.3
      jest-util: 28.1.3
      jest-validate: 28.1.3
      jest-watcher: 28.1.3
      micromatch: 4.0.4
      pretty-format: 28.1.3
      rimraf: 3.0.2
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - supports-color
      - ts-node
    dev: false

  /@jest/environment/28.1.3:
    resolution: {integrity: sha512-1bf40cMFTEkKyEf585R9Iz1WayDjHoHqvts0XFYEqyKM3cFWDpeMoqKKTAF9LSYQModPUlh8FKptoM2YcMWAXA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/fake-timers': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      jest-mock: 28.1.3
    dev: false

  /@jest/expect-utils/28.1.3:
    resolution: {integrity: sha512-wvbi9LUrHJLn3NlDW6wF2hvIMtd4JUl2QNVrjq+IBSHirgfrR3o9RnVtxzdEGO2n9JyIWwHnLfby5KzqBGg2YA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      jest-get-type: 28.0.2
    dev: false

  /@jest/expect/28.1.3:
    resolution: {integrity: sha512-lzc8CpUbSoE4dqT0U+g1qODQjBRHPpCPXissXD4mS9+sWQdmmpeJ9zSH1rS1HEkrsMN0fb7nKrJ9giAR1d3wBw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      expect: 28.1.3
      jest-snapshot: 28.1.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/fake-timers/28.1.3:
    resolution: {integrity: sha512-D/wOkL2POHv52h+ok5Oj/1gOG9HSywdoPtFsRCUmlCILXNn5eIWmcnd3DIiWlJnpGvQtmajqBP95Ei0EimxfLw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      '@sinonjs/fake-timers': 9.1.2
      '@types/node': 18.11.17
      jest-message-util: 28.1.3
      jest-mock: 28.1.3
      jest-util: 28.1.3
    dev: false

  /@jest/globals/28.1.3:
    resolution: {integrity: sha512-XFU4P4phyryCXu1pbcqMO0GSQcYe1IsalYCDzRNyhetyeyxMcIxa11qPNDpVNLeretItNqEmYYQn1UYz/5x1NA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.3
      '@jest/expect': 28.1.3
      '@jest/types': 28.1.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/reporters/28.1.3:
    resolution: {integrity: sha512-JuAy7wkxQZVNU/V6g9xKzCGC5LVXx9FDcABKsSXp5MiKPEE2144a/vXTEDoyzjUpZKfVwp08Wqg5A4WfTMAzjg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 28.1.3
      '@jest/test-result': 28.1.3
      '@jest/transform': 28.1.3
      '@jest/types': 28.1.3
      '@jridgewell/trace-mapping': 0.3.14
      '@types/node': 18.11.17
      chalk: 4.1.2
      collect-v8-coverage: 1.0.1
      exit: 0.1.2
      glob: 7.2.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      istanbul-lib-coverage: 3.2.0
      istanbul-lib-instrument: 5.2.1
      istanbul-lib-report: 3.0.0
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.5
      jest-message-util: 28.1.3
      jest-util: 28.1.3
      jest-worker: 28.1.3
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      terminal-link: 2.1.1
      v8-to-istanbul: 9.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/schemas/28.1.3:
    resolution: {integrity: sha512-/l/VWsdt/aBXgjshLWOFyFt3IVdYypu5y2Wn2rOO1un6nkqIn8SLXzgIMYXFyYsRWDyF5EthmKJMIdJvk08grg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@sinclair/typebox': 0.24.51
    dev: false

  /@jest/source-map/28.1.2:
    resolution: {integrity: sha512-cV8Lx3BeStJb8ipPHnqVw/IM2VCMWO3crWZzYodSIkxXnRcXJipCdx1JCK0K5MsJJouZQTH73mzf4vgxRaH9ww==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jridgewell/trace-mapping': 0.3.14
      callsites: 3.1.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
    dev: false

  /@jest/test-result/28.1.3:
    resolution: {integrity: sha512-kZAkxnSE+FqE8YjW8gNuoVkkC9I7S1qmenl8sGcDOLropASP+BkcGKwhXoyqQuGOGeYY0y/ixjrd/iERpEXHNg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/console': 28.1.3
      '@jest/types': 28.1.3
      '@types/istanbul-lib-coverage': 2.0.4
      collect-v8-coverage: 1.0.1
    dev: false

  /@jest/test-sequencer/28.1.3:
    resolution: {integrity: sha512-NIMPEqqa59MWnDi1kvXXpYbqsfQmSJsIbnd85mdVGkiDfQ9WQQTXOLsvISUfonmnBT+w85WEgneCigEEdHDFxw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/test-result': 28.1.3
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-haste-map: 28.1.3
      slash: 3.0.0
    dev: false

  /@jest/transform/28.1.3:
    resolution: {integrity: sha512-u5dT5di+oFI6hfcLOHGTAfmUxFRrjK+vnaP0kkVow9Md/M7V/MxqQMOz/VV25UZO8pzeA9PjfTpOu6BDuwSPQA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/core': 7.17.5
      '@jest/types': 28.1.3
      '@jridgewell/trace-mapping': 0.3.14
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 1.8.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-haste-map: 28.1.3
      jest-regex-util: 28.0.2
      jest-util: 28.1.3
      micromatch: 4.0.4
      pirates: 4.0.5
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@jest/types/28.1.3:
    resolution: {integrity: sha512-RyjiyMUZrKz/c+zlMFO1pm70DcIlST8AeWTkoUdZevew44wcNZQHsEVOiCVtgVnlFFD82FPaXycys58cf2muVQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/schemas': 28.1.3
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 3.0.1
      '@types/node': 18.11.17
      '@types/yargs': 17.0.17
      chalk: 4.1.2
    dev: false

  /@jimp/bmp/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-CZYQPEC3iUBMuaGWrtIG+GKNl93q/PkdudrCKJR/B96dfNngsmoosEm3LuFgJHEcJIfvnJkNqKw74l+zEiqCbg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      bmp-js: 0.1.0
      core-js: 3.21.1
    dev: false

  /@jimp/core/0.9.8:
    resolution: {integrity: sha512-N4GCjcXb0QwR5GBABDK2xQ3cKyaF7LlCYeJEG9mV7G/ynBoRqJe4JA6YKU9Ww9imGkci/4A594nQo8tUIqdcBw==}
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/utils': 0.9.8
      any-base: 1.1.0
      buffer: 5.7.1
      core-js: 3.21.1
      exif-parser: 0.1.12
      file-type: 9.0.0
      load-bmfont: 1.4.1
      mkdirp: 0.5.5
      phin: 2.9.3
      pixelmatch: 4.0.2
      tinycolor2: 1.4.2
    dev: false

  /@jimp/custom/0.9.8:
    resolution: {integrity: sha512-1UpJjI7fhX02BWLJ/KEqPwkHH60eNkCNeD6hEd+IZdTwLXfZCfFiM5BVlpgiZYZJSsVoRiAL4ne2Q5mCiKPKyw==}
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/core': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/gif/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-LEbfpcO1sBJIQCJHchZjNlyNxzPjZQQ4X32klpQHZJG58n9FvL7Uuh1rpkrJRbqv3cU3P0ENNtTrsBDxsYwcfA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
      omggif: 1.0.10
    dev: false

  /@jimp/jpeg/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-5u29SUzbZ32ZMmOaz3gO0hXatwSCnsvEAXRCKZoPPgbsPoyFAiZKVxjfLzjkeQF6awkvJ8hZni5chM15SNMg+g==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
      jpeg-js: 0.3.7
    dev: false

  /@jimp/plugin-blit/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-6xTDomxJybhBcby1IUVaPydZFhxf+V0DRgfDlVK81kR9kSCoshJpzWqDuWrMqjNEPspPE7jRQwHMs0FdU7mVwQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-blur/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-dqbxuNFBRbmt35iIRacdgma7nlXklmPThsKcGWNTDmqb/hniK5IC+0xSPzBV4qMI2fLGP39LWHqqDZ0xDz14dA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-circle/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-+UStXUPCzPqzTixLC8eVqcFcEa6TS+BEM/6/hyM11TDb9sbiMGeUtgpwZP/euR5H5gfpAQDA1Ppzqhh5fuMDlw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-color/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-SDHxOQsJHpt75hk6+sSlCPc2B3UJlXosFW+iLZ11xX1Qr0IdDtbfYlIoPmjKQFIDUNzqLSue/z7sKQ1OMZr/QA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
      tinycolor2: 1.4.2
    dev: false

  /@jimp/plugin-contain/0.9.8_14820836a11d26af006b0f8f99c5aa8d:
    resolution: {integrity: sha512-oK52CPt7efozuLYCML7qOmpFeDt3zpU8qq8UZlnjsDs15reU6L8EiUbwYpJvzoEnEOh1ZqamB8F/gymViEO5og==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
      '@jimp/plugin-scale': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-blit': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-scale': 0.9.8_7675d191481bcc5a4f8d7bacb2ea8e87
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-cover/0.9.8_27c062c0aca2011590dad8e68ccbd231:
    resolution: {integrity: sha512-nnamtHzMrNd5j5HRSPd1VzpZ8v9YYtUJPtvCdHOOiIjqG72jxJ2kTBlsS3oG5XS64h/2MJwpl/fmmMs1Tj1CmQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-crop': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
      '@jimp/plugin-scale': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-crop': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-scale': 0.9.8_7675d191481bcc5a4f8d7bacb2ea8e87
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-crop/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-Nv/6AIp4aJmbSIH2uiIqm+kSoShKM8eaX2fyrUTj811kio0hwD3f/vIxrWebvAqwDZjAFIAmMufFoFCVg6caoQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-displace/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-0OgPjkOVa2xdbqI8P6gBKX/UK36RbaYVrFyXL8Jy9oNF69+LYWyTskuCu9YbGxzlCVjY/JFqQOvrKDbxgMYAKA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-dither/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-jGM/4ByniZJnmV2fv8hKwyyydXZe/YzvgBcnB8XxzCq8kVR3Imcn+qnd2PEPZzIPKOTH4Cig/zo9Vk9Bs+m5FQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-fisheye/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-VnsalrD05f4pxG1msjnkwIFi5QveOqRm4y7VkoZKNX+iqs4TvRnH5+HpBnfdMzX/RXBi+Lf/kpTtuZgbOu/QWw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-flip/0.9.8_dfa540586960311474575a380c6e577f:
    resolution: {integrity: sha512-XbiZ4OfHD6woc0f6Sk7XxB6a7IyMjTRQ4pNU7APjaNxsl3L6qZC8qfCQphWVe3DHx7f3y7jEiPMvNnqRDP1xgA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-rotate': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-rotate': 0.9.8_8d1fc92b8d1c445c9cd127881736d00a
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-gaussian/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-ZBl5RA6+4XAD+mtqLfiG7u+qd8W5yqq3RBNca8eFqUSVo1v+eB2tzeLel0CWfVC/z6cw93Awm/nVnm6/CL2Oew==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-invert/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-ESploqCoF6qUv5IWhVLaO5fEcrYZEsAWPFflh6ROiD2mmFKQxfeK+vHnk3IDLHtUwWTkAZQNbk89BVq7xvaNpQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-mask/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-zSvEisTV4iGsBReitEdnQuGJq9/1xB5mPATadYZmIlp8r5HpD72HQb0WdEtb51/pu9Odt8KAxUf0ASg/PRVUiQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-normalize/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-dPFBfwTa67K1tRw1leCidQT25R3ozrTUUOpO4jcGFHqXvBTWaR8sML1qxdfOBWs164mE5YpfdTvu6MM/junvCg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-print/0.9.8_dd88529565f7fe576bb696982c308e0e:
    resolution: {integrity: sha512-nLLPv1/faehRsOjecXXUb6kzhRcZzImO55XuFZ0c90ZyoiHm4UFREwO5sKxHGvpLXS6RnkhvSav4+IWD2qGbEQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-blit': 0.9.8_@jimp+custom@0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
      load-bmfont: 1.4.1
    dev: false

  /@jimp/plugin-resize/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-L80NZ+HKsiKFyeDc6AfneC4+5XACrdL2vnyAVfAAsb3pmamgT/jDInWvvGhyI0Y76vx2w6XikplzEznW/QQvWg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-rotate/0.9.8_8d1fc92b8d1c445c9cd127881736d00a:
    resolution: {integrity: sha512-bpqzQheISYnBXKyU1lIj46uR7mRs0UhgEREWK70HnvFJSlRshdcoNMIrKamyrJeFdJrkYPSfR/a6D0d5zsWf1Q==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
      '@jimp/plugin-crop': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-blit': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-crop': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-scale/0.9.8_7675d191481bcc5a4f8d7bacb2ea8e87:
    resolution: {integrity: sha512-QU3ZS4Lre8nN66U9dKCOC4FNfaOh/QJFYUmQPKpPS924oYbtnm4OlmsdfpK2hVMSVVyVOis8M+xpA1rDBnIp7w==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-shadow/0.9.8_aab4e135f724a824a28df11473971b24:
    resolution: {integrity: sha512-t/pE+QS3r1ZUxGIQNmwWDI3c5+/hLU+gxXD+C3EEC47/qk3gTBHpj/xDdGQBoObdT/HRjR048vC2BgBfzjj2hg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blur': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-blur': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugin-threshold/0.9.8_30a0024797eaaa95040d498b2cf101a0:
    resolution: {integrity: sha512-WWmC3lnIwOTPvkKu55w4DUY8Ehlzf3nU98bY0QtIzkqxkAOZU5m+lvgC/JxO5FyGiA57j9FLMIf0LsWkjARj7g==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-color': '>=0.8.0'
      '@jimp/plugin-resize': '>=0.8.0'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-color': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
    dev: false

  /@jimp/plugins/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-tD+cxS9SuEZaQ1hhAkNKw9TkUAqfoBAhdWPBrEZDr/GvGPrvJR4pYmmpSYhc5IZmMbXfQayHTTGqjj8D18bToA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugin-blit': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-blur': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-circle': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-color': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-contain': 0.9.8_14820836a11d26af006b0f8f99c5aa8d
      '@jimp/plugin-cover': 0.9.8_27c062c0aca2011590dad8e68ccbd231
      '@jimp/plugin-crop': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-displace': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-dither': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-fisheye': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-flip': 0.9.8_dfa540586960311474575a380c6e577f
      '@jimp/plugin-gaussian': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-invert': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-mask': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-normalize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-print': 0.9.8_dd88529565f7fe576bb696982c308e0e
      '@jimp/plugin-resize': 0.9.8_@jimp+custom@0.9.8
      '@jimp/plugin-rotate': 0.9.8_8d1fc92b8d1c445c9cd127881736d00a
      '@jimp/plugin-scale': 0.9.8_7675d191481bcc5a4f8d7bacb2ea8e87
      '@jimp/plugin-shadow': 0.9.8_aab4e135f724a824a28df11473971b24
      '@jimp/plugin-threshold': 0.9.8_30a0024797eaaa95040d498b2cf101a0
      core-js: 3.21.1
      timm: 1.7.1
    dev: false

  /@jimp/png/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-9CqR8d40zQCDhbnXHqcwkAMnvlV0vk9xSyE6LHjkYHS7x18Unsz5txQdsaEkEcXxCrOQSoWyITfLezlrWXRJAA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/utils': 0.9.8
      core-js: 3.21.1
      pngjs: 3.4.0
    dev: false

  /@jimp/tiff/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-eMxcpJivJqMByn2dZxUHLeh6qvVs5J/52kBF3TFa3C922OJ97D9l1C1h0WKUCBqFMWzMYapQQ4vwnLgpJ5tkow==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      core-js: 3.21.1
      utif: 2.0.1
    dev: false

  /@jimp/types/0.9.8_@jimp+custom@0.9.8:
    resolution: {integrity: sha512-H5y/uqt0lqJ/ZN8pWqFG+pv8jPAppMKkTMByuC8YBIjWSsornwv44hjiWl93sbYhduLZY8ubz/CbX9jH2X6EwA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/bmp': 0.9.8_@jimp+custom@0.9.8
      '@jimp/custom': 0.9.8
      '@jimp/gif': 0.9.8_@jimp+custom@0.9.8
      '@jimp/jpeg': 0.9.8_@jimp+custom@0.9.8
      '@jimp/png': 0.9.8_@jimp+custom@0.9.8
      '@jimp/tiff': 0.9.8_@jimp+custom@0.9.8
      core-js: 3.21.1
      timm: 1.7.1
    dev: false

  /@jimp/utils/0.9.8:
    resolution: {integrity: sha512-UK0Fu0eevQlpRXq5ff4o/71HJlpX9wJMddJjMYg9vUqCCl8ZnumRAljfShHFhGyO+Vc9IzN6dd8Y5JZZTp1KOw==}
    dependencies:
      '@babel/runtime': 7.17.2
      core-js: 3.21.1
    dev: false

  /@jridgewell/gen-mapping/0.1.1:
    resolution: {integrity: sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.11
    dev: false

  /@jridgewell/gen-mapping/0.3.2:
    resolution: {integrity: sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.11
      '@jridgewell/trace-mapping': 0.3.14
    dev: false

  /@jridgewell/resolve-uri/3.0.5:
    resolution: {integrity: sha512-VPeQ7+wH0itvQxnG+lIzWgkysKIr3L9sslimFW55rHMdGu/qCQ5z5h9zq4gI8uBtqkpHhsF4Z/OwExufUCThew==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/set-array/1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/source-map/0.3.2:
    resolution: {integrity: sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.2
      '@jridgewell/trace-mapping': 0.3.14
    dev: false

  /@jridgewell/sourcemap-codec/1.4.11:
    resolution: {integrity: sha512-Fg32GrJo61m+VqYSdRSjRXMjQ06j8YIYfcTqndLYVAaHmroZHLJZCydsWBOTDqXS2v+mjxohBWEMfg97GXmYQg==}
    dev: false

  /@jridgewell/trace-mapping/0.3.14:
    resolution: {integrity: sha512-bJWEfQ9lPTvm3SneWwRFVLzrh6nhjwqw7TUFFBEMzwvg7t7PCDenf2lDwqo4NQXzdpgBXyFgDWnQA+2vkruksQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.0.5
      '@jridgewell/sourcemap-codec': 1.4.11
    dev: false

  /@jridgewell/trace-mapping/0.3.4:
    resolution: {integrity: sha512-vFv9ttIedivx0ux3QSjhgtCVjPZd5l46ZOMDSCwnH1yUO2e964gO8LZGyv2QkqcgR6TnBU1v+1IFqmeoG+0UJQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.0.5
      '@jridgewell/sourcemap-codec': 1.4.11
    dev: false

  /@leejim/wxml-parser/0.1.6:
    resolution: {integrity: sha512-1u4ULGK4GKkWhTlc3Hmac8PknrmpGd7qxZOTnT/Bm6EZ/wtonLgFhJ4vyuiUZpeCptnknOLkRpGx2Um9npwdZw==}
    dev: false

  /@mantas/request/1.0.12:
    resolution: {integrity: sha512-LoKyhmaTjwPn4G4vkYMwCPN1Ucq+CuziPYtmKpLKzom/XyopiyqOWyE2bHZzUdkvR2vv3CVXPi3r0X2NknrvAw==}
    dependencies:
      '@rollup/plugin-node-resolve': 13.3.0_rollup@2.79.1
      create-test-server: 3.0.1
      jest: 28.1.3
      lodash: 4.17.21
      rollup: 2.79.1
      rollup-plugin-analyzer: 4.0.0
      rollup-plugin-commonjs: 10.1.0_rollup@2.79.1
      rollup-plugin-delete: 2.0.0
      rollup-plugin-terser: 7.0.2_rollup@2.79.1
      rollup-plugin-typescript2: 0.32.1_rollup@2.79.1+typescript@4.9.4
      ts-jest: 28.0.8_jest@28.1.3+typescript@4.9.4
      tslib: 2.4.0
      typescript: 4.9.4
      umi-request: 1.4.0
    transitivePeerDependencies:
      - '@babel/core'
      - '@jest/types'
      - '@types/node'
      - babel-jest
      - esbuild
      - node-notifier
      - supports-color
      - ts-node
    dev: false

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: false

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: false

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.13.0
    dev: false

  /@rollup/plugin-node-resolve/13.3.0_rollup@2.79.1:
    resolution: {integrity: sha512-Lus8rbUo1eEcnS4yTFKLZrVumLPY+YayBdWXgFSHYhTT2iJbMhoaaBL3xl5NCdeRytErGr8tZ0L71BMRmnlwSw==}
    engines: {node: '>= 10.0.0'}
    peerDependencies:
      rollup: ^2.42.0
    dependencies:
      '@rollup/pluginutils': 3.1.0_rollup@2.79.1
      '@types/resolve': 1.17.1
      deepmerge: 4.2.2
      is-builtin-module: 3.2.0
      is-module: 1.0.0
      resolve: 1.22.0
      rollup: 2.79.1
    dev: false

  /@rollup/pluginutils/3.1.0_rollup@2.79.1:
    resolution: {integrity: sha512-GksZ6pr6TpIjHm8h9lSQ8pi8BE9VeubNT0OMJ3B5uZJ8pz73NPiqOtCog/x2/QzM1ENChPKxMDhiQuRHsqc+lg==}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0
    dependencies:
      '@types/estree': 0.0.39
      estree-walker: 1.0.1
      picomatch: 2.3.1
      rollup: 2.79.1
    dev: false

  /@rollup/pluginutils/4.2.1:
    resolution: {integrity: sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      estree-walker: 2.0.2
      picomatch: 2.3.1
    dev: false

  /@sinclair/typebox/0.24.51:
    resolution: {integrity: sha512-1P1OROm/rdubP5aFDSZQILU0vrLCJ4fvHt6EoqHEM+2D/G5MK3bIaymUKLit8Js9gbns5UyJnkP/TZROLw4tUA==}
    dev: false

  /@sinonjs/commons/1.8.6:
    resolution: {integrity: sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==}
    dependencies:
      type-detect: 4.0.8
    dev: false

  /@sinonjs/fake-timers/9.1.2:
    resolution: {integrity: sha512-BPS4ynJW/o92PUR4wgriz2Ud5gpST5vz6GQfMixEDK0Z8ZCUv2M7SkBLykH56T++Xs+8ln9zTGbOvNGIe02/jw==}
    dependencies:
      '@sinonjs/commons': 1.8.6
    dev: false

  /@trysound/sax/0.2.0:
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}
    dev: false

  /@types/babel__core/7.1.20:
    resolution: {integrity: sha512-PVb6Bg2QuscZ30FvOU7z4guG6c926D9YRvOxEaelzndpMsvP+YM74Q/dAFASpg2l6+XLalxSGxcq/lrgYWZtyQ==}
    dependencies:
      '@babel/parser': 7.18.11
      '@babel/types': 7.18.10
      '@types/babel__generator': 7.6.4
      '@types/babel__template': 7.4.1
      '@types/babel__traverse': 7.18.3
    dev: false

  /@types/babel__generator/7.6.4:
    resolution: {integrity: sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@types/babel__template/7.4.1:
    resolution: {integrity: sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==}
    dependencies:
      '@babel/parser': 7.18.11
      '@babel/types': 7.18.10
    dev: false

  /@types/babel__traverse/7.18.3:
    resolution: {integrity: sha512-1kbcJ40lLB7MHsj39U4Sh1uTd2E7rLEa79kmDpI6cy+XiXsteB3POdQomoq4FxszMrO3ZYchkhYJw7A2862b3w==}
    dependencies:
      '@babel/types': 7.18.10
    dev: false

  /@types/estree/0.0.39:
    resolution: {integrity: sha512-EYNwp3bU+98cpU4lAWYYL7Zz+2gryWH1qbdDTidVd6hkiR6weksdbMadyXKXNPEkQFhXM+hVO9ZygomHXp+AIw==}
    dev: false

  /@types/estree/1.0.0:
    resolution: {integrity: sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==}
    dev: false

  /@types/glob/7.2.0:
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=}
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 18.11.17
    dev: false

  /@types/graceful-fs/4.1.5:
    resolution: {integrity: sha512-anKkLmZZ+xm4p8JWBf4hElkM4XR+EZeA2M9BAkkTldmcyDY4mbdIJnRghDJH3Ov5ooY7/UAoENtmdMSkaAd7Cw==}
    dependencies:
      '@types/node': 18.11.17
    dev: false

  /@types/istanbul-lib-coverage/2.0.4:
    resolution: {integrity: sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==}
    dev: false

  /@types/istanbul-lib-report/3.0.0:
    resolution: {integrity: sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
    dev: false

  /@types/istanbul-reports/3.0.1:
    resolution: {integrity: sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==}
    dependencies:
      '@types/istanbul-lib-report': 3.0.0
    dev: false

  /@types/minimatch/5.1.2:
    resolution: {integrity: sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==}
    dev: false

  /@types/node/18.11.17:
    resolution: {integrity: sha512-HJSUJmni4BeDHhfzn6nF0sVmd1SMezP7/4F0Lq+aXzmp2xm9O7WXrUtHW/CHlYVtZUbByEvWidHqRtcJXGF2Ng==}
    dev: false

  /@types/parse-json/4.0.0:
    resolution: {integrity: sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==}
    dev: true

  /@types/prettier/2.7.2:
    resolution: {integrity: sha512-KufADq8uQqo1pYKVIYzfKbJfBAc0sOeXqGbFaSpv8MRmC/zXgowNZmFcbngndGk922QDmOASEXUZCaY48gs4cg==}
    dev: false

  /@types/resolve/1.17.1:
    resolution: {integrity: sha512-yy7HuzQhj0dhGpD8RLXSZWEkLsV9ibvxvi6EiJ3bkqLAO1RGo0WbkWQiwpRlSFymTJRz0d3k5LM3kkx8ArDbLw==}
    dependencies:
      '@types/node': 18.11.17
    dev: false

  /@types/stack-utils/2.0.1:
    resolution: {integrity: sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw==}
    dev: false

  /@types/yargs-parser/21.0.0:
    resolution: {integrity: sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==}
    dev: false

  /@types/yargs/17.0.17:
    resolution: {integrity: sha512-72bWxFKTK6uwWJAVT+3rF6Jo6RTojiJ27FQo8Rf60AL+VZbzoVPnMFhKsUnbjR8A3BTCYQ7Mv3hnl8T0A+CX9g==}
    dependencies:
      '@types/yargs-parser': 21.0.0
    dev: false

  /@vue/reactivity/3.0.5:
    resolution: {integrity: sha512-3xodUE3sEIJgS7ntwUbopIpzzvi7vDAOjVamfb2l+v1FUg0jpd3gf62N2wggJw3fxBMr+QvyxpD+dBoxLsmAjw==}
    dependencies:
      '@vue/shared': 3.0.5
    dev: false

  /@vue/shared/3.0.5:
    resolution: {integrity: sha512-gYsNoGkWejBxNO6SNRjOh/xKeZ0H0V+TFzaPzODfBjkAIb0aQgBuixC1brandC/CDJy1wYPwSoYrXpvul7m6yw==}
    dev: false

  /accepts/1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3
    dev: false

  /acorn/6.4.2:
    resolution: {integrity: sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /aggregate-error/3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: false

  /ajv-formats/1.6.1:
    resolution: {integrity: sha512-4CjkH20If1lhR5CGtqkrVg3bbOtFEG80X9v6jDOIUhbzzbB+UzPBGy8GQhUNVZ0yvMHdMpawCOcy5ydGMsagGQ==}
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 7.2.4
    dev: false

  /ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: false

  /ajv/7.2.4:
    resolution: {integrity: sha512-nBeQgg/ZZA3u3SYxyaDvpvDtgZ/EZPF547ARgZBrG9Bhu1vKDwAIjtIf+sDtJUKa2zOcEbmRLBRSyMraS/Oy1A==}
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1
    dev: false

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: false

  /ansi-regex/2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-regex/5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}
    dev: false

  /ansi-styles/2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /ansi-styles/5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}
    dev: false

  /any-base/1.1.0:
    resolution: {integrity: sha512-uMgjozySS8adZZYePpaWs8cxB9/kdzmpX6SgJZ+wbz1K5eYk5QMYDVJaZKhxyIHUdnnJkfR7SVgStgH7LkGUyg==}
    dev: false

  /anymatch/3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: false

  /argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: false

  /array-flatten/1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=}
    dev: false

  /array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: false

  /asap/2.0.6:
    resolution: {integrity: sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=}
    dev: false

  /asn1/0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /assert-plus/1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=}
    engines: {node: '>=0.8'}
    dev: false

  /asynckit/0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}
    dev: false

  /atomically/1.7.0:
    resolution: {integrity: sha512-Xcz9l0z7y9yQ9rdDaxlmaI4uJHf/T8g9hOEzJcsEqX2SjCj4J20uK7+ldkDHMbpJDK76wF7xEIgxc/vSlsfw5w==}
    engines: {node: '>=10.12.0'}
    dev: false

  /autoprefixer/10.4.8_postcss@8.4.14:
    resolution: {integrity: sha512-75Jr6Q/XpTqEf6D2ltS5uMewJIx5irCU1oBYJrWjFenq/m12WRRrz6g15L1EIoYvPLXTbEry7rDOwrcYNj77xw==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.21.3
      caniuse-lite: 1.0.30001374
      fraction.js: 4.2.0
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /aws-sign2/0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=}
    dev: false

  /aws4/1.11.0:
    resolution: {integrity: sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA==}
    dev: false

  /babel-code-frame/6.26.0:
    resolution: {integrity: sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=}
    dependencies:
      chalk: 1.1.3
      esutils: 2.0.3
      js-tokens: registry.npmmirror.com/js-tokens/3.0.2
    dev: false

  /babel-core/6.26.0:
    resolution: {integrity: sha1-rzL3izGm/O8RnIew/Y2XU/A6C7g=}
    dependencies:
      babel-code-frame: 6.26.0
      babel-generator: 6.26.1
      babel-helpers: 6.24.1
      babel-messages: 6.23.0
      babel-register: 6.26.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      convert-source-map: 1.8.0
      debug: 2.6.9
      json5: 0.5.1
      lodash: 4.17.21
      minimatch: 3.1.2
      path-is-absolute: 1.0.1
      private: 0.1.8
      slash: 1.0.0
      source-map: registry.npmmirror.com/source-map/0.5.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-generator/6.26.1:
    resolution: {integrity: sha512-HyfwY6ApZj7BYTcJURpM5tznulaBvyio7/0d4zFOeMPUmfxkCjHocCuoLa2SAGzBI8AREcH3eP3758F672DppA==}
    dependencies:
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      detect-indent: 4.0.0
      jsesc: 1.3.0
      lodash: 4.17.21
      source-map: registry.npmmirror.com/source-map/0.5.7
      trim-right: 1.0.1
    dev: false

  /babel-helper-bindify-decorators/6.24.1:
    resolution: {integrity: sha1-FMGeXxQte0fxmlJDHlKxzLxAozA=}
    dependencies:
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-builder-binary-assignment-operator-visitor/6.24.1:
    resolution: {integrity: sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=}
    dependencies:
      babel-helper-explode-assignable-expression: 6.24.1
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-call-delegate/6.24.1:
    resolution: {integrity: sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=}
    dependencies:
      babel-helper-hoist-variables: 6.24.1
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-define-map/6.26.0:
    resolution: {integrity: sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=}
    dependencies:
      babel-helper-function-name: 6.24.1
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-explode-assignable-expression/6.24.1:
    resolution: {integrity: sha1-8luCz33BBDPFX3BZLVdGQArCLKo=}
    dependencies:
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-explode-class/6.24.1:
    resolution: {integrity: sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes=}
    dependencies:
      babel-helper-bindify-decorators: 6.24.1
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-function-name/6.24.1:
    resolution: {integrity: sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=}
    dependencies:
      babel-helper-get-function-arity: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-get-function-arity/6.24.1:
    resolution: {integrity: sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-helper-hoist-variables/6.24.1:
    resolution: {integrity: sha1-HssnaJydJVE+rbyZFKc/VAi+enY=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-helper-optimise-call-expression/6.24.1:
    resolution: {integrity: sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-helper-regex/6.26.0:
    resolution: {integrity: sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      lodash: 4.17.21
    dev: false

  /babel-helper-remap-async-to-generator/6.24.1:
    resolution: {integrity: sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=}
    dependencies:
      babel-helper-function-name: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-replace-supers/6.24.1:
    resolution: {integrity: sha1-v22/5Dk40XNpohPKiov3S2qQqxo=}
    dependencies:
      babel-helper-optimise-call-expression: 6.24.1
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helpers/6.24.1:
    resolution: {integrity: sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=}
    dependencies:
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-jest/28.1.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-epUaPOEWMk3cWX0M/sPvCHHCe9fMFAa/9hXEgKP8nFfNl/jlGkE9ucq9NqkZGXLDduCJYS0UvSlPUwC0S+rH6Q==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0
    dependencies:
      '@babel/core': 7.17.5
      '@jest/transform': 28.1.3
      '@types/babel__core': 7.1.20
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 28.1.3_@babel+core@7.17.5
      chalk: 4.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-messages/6.23.0:
    resolution: {integrity: sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-check-es2015-constants/6.22.0:
    resolution: {integrity: sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-dynamic-import-node/2.3.3:
    resolution: {integrity: sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==}
    dependencies:
      object.assign: 4.1.2
    dev: false

  /babel-plugin-istanbul/6.1.1:
    resolution: {integrity: sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=}
    engines: {node: '>=8'}
    dependencies:
      '@babel/helper-plugin-utils': 7.18.9
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-jest-hoist/28.1.3:
    resolution: {integrity: sha512-Ys3tUKAmfnkRUpPdpa98eYrAR0nV+sSFUZZEGuQ2EbFd1y4SOLtD5QDNHAq+bb9a+bbXvYQC4b+ID/THIMcU6Q==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/types': 7.18.10
      '@types/babel__core': 7.1.20
      '@types/babel__traverse': 7.18.3
    dev: false

  /babel-plugin-syntax-async-functions/6.13.0:
    resolution: {integrity: sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU=}
    dev: false

  /babel-plugin-syntax-async-generators/6.13.0:
    resolution: {integrity: sha1-a8lj67FuzLrmuStZbrfzXDQqi5o=}
    dev: false

  /babel-plugin-syntax-class-constructor-call/6.18.0:
    resolution: {integrity: sha1-nLnTn+Q8hgC+yBRkVt3L1OGnZBY=}
    dev: false

  /babel-plugin-syntax-class-properties/6.13.0:
    resolution: {integrity: sha1-1+sjt5oxf4VDlixQW4J8fWysJ94=}
    dev: false

  /babel-plugin-syntax-decorators/6.13.0:
    resolution: {integrity: sha1-MSVjtNvePMgGzuPkFszurd0RrAs=}
    dev: false

  /babel-plugin-syntax-do-expressions/6.13.0:
    resolution: {integrity: sha1-V0d1YTmqJtOQ0JQQsDdEugfkeW0=}
    dev: false

  /babel-plugin-syntax-dynamic-import/6.18.0:
    resolution: {integrity: sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo=}
    dev: false

  /babel-plugin-syntax-exponentiation-operator/6.13.0:
    resolution: {integrity: sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4=}
    dev: false

  /babel-plugin-syntax-export-extensions/6.13.0:
    resolution: {integrity: sha1-cKFITw+QiaToStRLrDU8lbmxJyE=}
    dev: false

  /babel-plugin-syntax-function-bind/6.13.0:
    resolution: {integrity: sha1-SMSV8Xe98xqYHnMvVa3AvdJgH0Y=}
    dev: false

  /babel-plugin-syntax-object-rest-spread/6.13.0:
    resolution: {integrity: sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=}
    dev: false

  /babel-plugin-syntax-trailing-function-commas/6.22.0:
    resolution: {integrity: sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM=}
    dev: false

  /babel-plugin-transform-async-generator-functions/6.24.1:
    resolution: {integrity: sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds=}
    dependencies:
      babel-helper-remap-async-to-generator: 6.24.1
      babel-plugin-syntax-async-generators: 6.13.0
      babel-runtime: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-async-to-generator/6.24.1:
    resolution: {integrity: sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=}
    dependencies:
      babel-helper-remap-async-to-generator: 6.24.1
      babel-plugin-syntax-async-functions: 6.13.0
      babel-runtime: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-class-constructor-call/6.24.1:
    resolution: {integrity: sha1-gNwoVQWsBn3LjWxl4vbxGrd2Xvk=}
    dependencies:
      babel-plugin-syntax-class-constructor-call: 6.18.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-class-properties/6.24.1:
    resolution: {integrity: sha1-anl2PqYdM9NvN7YRqp3vgagbRqw=}
    dependencies:
      babel-helper-function-name: 6.24.1
      babel-plugin-syntax-class-properties: 6.13.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-decorators/6.24.1:
    resolution: {integrity: sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0=}
    dependencies:
      babel-helper-explode-class: 6.24.1
      babel-plugin-syntax-decorators: 6.13.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-do-expressions/6.22.0:
    resolution: {integrity: sha1-KMyvkoEtlJws0SgfaQyP3EaK6bs=}
    dependencies:
      babel-plugin-syntax-do-expressions: 6.13.0
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-arrow-functions/6.22.0:
    resolution: {integrity: sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-block-scoped-functions/6.22.0:
    resolution: {integrity: sha1-u8UbSflk1wy42OC5ToICRs46YUE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-block-scoping/6.26.0:
    resolution: {integrity: sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=}
    dependencies:
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-classes/6.24.1:
    resolution: {integrity: sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=}
    dependencies:
      babel-helper-define-map: 6.26.0
      babel-helper-function-name: 6.24.1
      babel-helper-optimise-call-expression: 6.24.1
      babel-helper-replace-supers: 6.24.1
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-computed-properties/6.24.1:
    resolution: {integrity: sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=}
    dependencies:
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-destructuring/6.23.0:
    resolution: {integrity: sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-duplicate-keys/6.24.1:
    resolution: {integrity: sha1-c+s9MQypaePvnskcU3QabxV2Qj4=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-for-of/6.23.0:
    resolution: {integrity: sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-function-name/6.24.1:
    resolution: {integrity: sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=}
    dependencies:
      babel-helper-function-name: 6.24.1
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-literals/6.22.0:
    resolution: {integrity: sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-modules-amd/6.24.1:
    resolution: {integrity: sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=}
    dependencies:
      babel-plugin-transform-es2015-modules-commonjs: 6.26.2
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-modules-commonjs/6.26.2:
    resolution: {integrity: sha512-CV9ROOHEdrjcwhIaJNBGMBCodN+1cfkwtM1SbUHmvyy35KGT7fohbpOxkE2uLz1o6odKK2Ck/tz47z+VqQfi9Q==}
    dependencies:
      babel-plugin-transform-strict-mode: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-modules-systemjs/6.24.1:
    resolution: {integrity: sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=}
    dependencies:
      babel-helper-hoist-variables: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-modules-umd/6.24.1:
    resolution: {integrity: sha1-rJl+YoXNGO1hdq22B9YCNErThGg=}
    dependencies:
      babel-plugin-transform-es2015-modules-amd: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-object-super/6.24.1:
    resolution: {integrity: sha1-JM72muIcuDp/hgPa0CH1cusnj40=}
    dependencies:
      babel-helper-replace-supers: 6.24.1
      babel-runtime: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-parameters/6.24.1:
    resolution: {integrity: sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=}
    dependencies:
      babel-helper-call-delegate: 6.24.1
      babel-helper-get-function-arity: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-es2015-shorthand-properties/6.24.1:
    resolution: {integrity: sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-spread/6.22.0:
    resolution: {integrity: sha1-1taKmfia7cRTbIGlQujdnxdG+NE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-sticky-regex/6.24.1:
    resolution: {integrity: sha1-AMHNsaynERLN8M9hJsLta0V8zbw=}
    dependencies:
      babel-helper-regex: 6.26.0
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-template-literals/6.22.0:
    resolution: {integrity: sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-typeof-symbol/6.23.0:
    resolution: {integrity: sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-es2015-unicode-regex/6.24.1:
    resolution: {integrity: sha1-04sS9C6nMj9yk4fxinxa4frrNek=}
    dependencies:
      babel-helper-regex: 6.26.0
      babel-runtime: 6.26.0
      regexpu-core: 2.0.0
    dev: false

  /babel-plugin-transform-exponentiation-operator/6.24.1:
    resolution: {integrity: sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=}
    dependencies:
      babel-helper-builder-binary-assignment-operator-visitor: 6.24.1
      babel-plugin-syntax-exponentiation-operator: 6.13.0
      babel-runtime: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-transform-export-extensions/6.22.0:
    resolution: {integrity: sha1-U3OLR+deghhYnuqUbLvTkQm75lM=}
    dependencies:
      babel-plugin-syntax-export-extensions: 6.13.0
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-function-bind/6.22.0:
    resolution: {integrity: sha1-xvuOlqwpajELjPjqQBRiQH3fapc=}
    dependencies:
      babel-plugin-syntax-function-bind: 6.13.0
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-object-rest-spread/6.26.0:
    resolution: {integrity: sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY=}
    dependencies:
      babel-plugin-syntax-object-rest-spread: 6.13.0
      babel-runtime: 6.26.0
    dev: false

  /babel-plugin-transform-regenerator/6.26.0:
    resolution: {integrity: sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=}
    dependencies:
      regenerator-transform: 0.10.1
    dev: false

  /babel-plugin-transform-strict-mode/6.24.1:
    resolution: {integrity: sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: false

  /babel-preset-current-node-syntax/1.0.1_@babel+core@7.17.5:
    resolution: {integrity: sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.17.5
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.17.5
      '@babel/plugin-syntax-bigint': 7.8.3_@babel+core@7.17.5
      '@babel/plugin-syntax-class-properties': 7.12.13_@babel+core@7.17.5
      '@babel/plugin-syntax-import-meta': 7.10.4_@babel+core@7.17.5
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.17.5
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.17.5
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.17.5
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.17.5
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.17.5
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.17.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.17.5
      '@babel/plugin-syntax-top-level-await': 7.14.5_@babel+core@7.17.5
    dev: false

  /babel-preset-es2015/6.24.1:
    resolution: {integrity: sha1-1EBQ1rwsn+6nAqrzjXJ6AhBTiTk=}
    deprecated: '🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!'
    dependencies:
      babel-plugin-check-es2015-constants: 6.22.0
      babel-plugin-transform-es2015-arrow-functions: 6.22.0
      babel-plugin-transform-es2015-block-scoped-functions: 6.22.0
      babel-plugin-transform-es2015-block-scoping: 6.26.0
      babel-plugin-transform-es2015-classes: 6.24.1
      babel-plugin-transform-es2015-computed-properties: 6.24.1
      babel-plugin-transform-es2015-destructuring: 6.23.0
      babel-plugin-transform-es2015-duplicate-keys: 6.24.1
      babel-plugin-transform-es2015-for-of: 6.23.0
      babel-plugin-transform-es2015-function-name: 6.24.1
      babel-plugin-transform-es2015-literals: 6.22.0
      babel-plugin-transform-es2015-modules-amd: 6.24.1
      babel-plugin-transform-es2015-modules-commonjs: 6.26.2
      babel-plugin-transform-es2015-modules-systemjs: 6.24.1
      babel-plugin-transform-es2015-modules-umd: 6.24.1
      babel-plugin-transform-es2015-object-super: 6.24.1
      babel-plugin-transform-es2015-parameters: 6.24.1
      babel-plugin-transform-es2015-shorthand-properties: 6.24.1
      babel-plugin-transform-es2015-spread: 6.22.0
      babel-plugin-transform-es2015-sticky-regex: 6.24.1
      babel-plugin-transform-es2015-template-literals: 6.22.0
      babel-plugin-transform-es2015-typeof-symbol: 6.23.0
      babel-plugin-transform-es2015-unicode-regex: 6.24.1
      babel-plugin-transform-regenerator: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-preset-jest/28.1.3_@babel+core@7.17.5:
    resolution: {integrity: sha512-L+fupJvlWAHbQfn74coNX3zf60LXMJsezNvvx8eIh7iOR1luJ1poxYgQk1F8PYtNq/6QODDHCqsSnTFSWC491A==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.17.5
      babel-plugin-jest-hoist: 28.1.3
      babel-preset-current-node-syntax: 1.0.1_@babel+core@7.17.5
    dev: false

  /babel-preset-stage-0/6.24.1:
    resolution: {integrity: sha1-VkLRUEL5E4TX5a+LyIsduVsDnmo=}
    dependencies:
      babel-plugin-transform-do-expressions: 6.22.0
      babel-plugin-transform-function-bind: 6.22.0
      babel-preset-stage-1: 6.24.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-preset-stage-1/6.24.1:
    resolution: {integrity: sha1-dpLNfc1oSZB+auSgqFWJz7niv7A=}
    dependencies:
      babel-plugin-transform-class-constructor-call: 6.24.1
      babel-plugin-transform-export-extensions: 6.22.0
      babel-preset-stage-2: 6.24.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-preset-stage-2/6.24.1:
    resolution: {integrity: sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE=}
    dependencies:
      babel-plugin-syntax-dynamic-import: 6.18.0
      babel-plugin-transform-class-properties: 6.24.1
      babel-plugin-transform-decorators: 6.24.1
      babel-preset-stage-3: 6.24.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-preset-stage-3/6.24.1:
    resolution: {integrity: sha1-g2raCp56f6N8sTj7kyb4eTSkg5U=}
    dependencies:
      babel-plugin-syntax-trailing-function-commas: 6.22.0
      babel-plugin-transform-async-generator-functions: 6.24.1
      babel-plugin-transform-async-to-generator: 6.24.1
      babel-plugin-transform-exponentiation-operator: 6.24.1
      babel-plugin-transform-object-rest-spread: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-register/6.26.0:
    resolution: {integrity: sha1-btAhFz4vy0htestFxgCahW9kcHE=}
    dependencies:
      babel-core: 6.26.0
      babel-runtime: 6.26.0
      core-js: 2.6.12
      home-or-tmp: 2.0.0
      lodash: 4.17.21
      mkdirp: 0.5.5
      source-map-support: 0.4.18
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-runtime/6.26.0:
    resolution: {integrity: sha1-llxwWGaOgrVde/4E/yM3vItWR/4=}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: false

  /babel-template/6.26.0:
    resolution: {integrity: sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=}
    dependencies:
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-traverse/6.26.0:
    resolution: {integrity: sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=}
    dependencies:
      babel-code-frame: 6.26.0
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      debug: 2.6.9
      globals: 9.18.0
      invariant: 2.2.4
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-types/6.26.0:
    resolution: {integrity: sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=}
    dependencies:
      babel-runtime: 6.26.0
      esutils: registry.npmmirror.com/esutils/2.0.3
      lodash: 4.17.21
      to-fast-properties: 1.0.3
    dev: false

  /babylon/6.18.0:
    resolution: {integrity: sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==}
    hasBin: true
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: false

  /base64-js/1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}
    dev: false

  /bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=}
    dependencies:
      tweetnacl: 0.14.5
    dev: false

  /binary-extensions/2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}
    dev: false

  /bl/4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.0
    dev: false

  /bmp-js/0.1.0:
    resolution: {integrity: sha1-4Fpj95amwf8l9Hcex62twUjAcjM=}
    dev: false

  /body-parser/1.20.1:
    resolution: {integrity: sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.4
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /boolbase/1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=}
    dev: false

  /brace-expansion/1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: false

  /braces/3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1
    dev: false

  /browserslist/4.19.3:
    resolution: {integrity: sha512-XK3X4xtKJ+Txj8G5c30B4gsm71s69lqXlkYui4s6EkKxuv49qjYlY6oVd+IFJ73d4YymtM3+djvvt/R/iJwwDg==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001312
      electron-to-chromium: 1.4.71
      escalade: 3.1.1
      node-releases: 2.0.2
      picocolors: 1.0.0
    dev: false

  /browserslist/4.21.3:
    resolution: {integrity: sha512-898rgRXLAyRkM1GryrrBHGkqA5hlpkV5MhtZwg9QXeiyLUYs2k00Un05aX5l2/yJIOObYKOpS2JNo8nJDE7fWQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001374
      electron-to-chromium: 1.4.211
      node-releases: 2.0.6
      update-browserslist-db: 1.0.5_browserslist@4.21.3
    dev: false

  /bs-logger/0.2.6:
    resolution: {integrity: sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==}
    engines: {node: '>= 6'}
    dependencies:
      fast-json-stable-stringify: 2.1.0
    dev: false

  /bser/2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}
    dependencies:
      node-int64: 0.4.0
    dev: false

  /buffer-equal/0.0.1:
    resolution: {integrity: sha1-kbx0sR6kBbyRa8aqkI+q+ltKrEs=}
    engines: {node: '>=0.4.0'}
    dev: false

  /buffer-from/1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: false

  /buffer/5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: false

  /builtin-modules/3.3.0:
    resolution: {integrity: sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==}
    engines: {node: '>=6'}
    dev: false

  /bytes/3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}
    dev: false

  /call-bind/1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.1.1
    dev: false

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  /camel-case/3.0.0:
    resolution: {integrity: sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=}
    dependencies:
      no-case: registry.npmmirror.com/no-case/2.3.2
      upper-case: registry.npmmirror.com/upper-case/1.1.3
    dev: false

  /camelcase/5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: false

  /camelcase/6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}
    dev: false

  /caniuse-api/3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}
    dependencies:
      browserslist: 4.21.3
      caniuse-lite: 1.0.30001374
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0
    dev: false

  /caniuse-lite/1.0.30001312:
    resolution: {integrity: sha512-Wiz1Psk2MEK0pX3rUzWaunLTZzqS2JYZFzNKqAiJGiuxIjRPLgV6+VDPOg6lQOUxmDwhTlh198JsTTi8Hzw6aQ==}
    dev: false

  /caniuse-lite/1.0.30001374:
    resolution: {integrity: sha512-mWvzatRx3w+j5wx/mpFN5v5twlPrabG8NqX2c6e45LCpymdoGqNvRkRutFUqpRTXKFQFNQJasvK0YT7suW6/Hw==}
    dev: false

  /caseless/0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=}
    dev: false

  /chalk/1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: false

  /chalk/2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  /char-regex/1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==}
    engines: {node: '>=10'}
    dev: false

  /chardet/0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}
    dev: false

  /charenc/0.0.2:
    resolution: {integrity: sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=}
    dev: false

  /chokidar/3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.2
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: false

  /ci-info/2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}
    dev: true

  /ci-info/3.7.0:
    resolution: {integrity: sha512-2CpRNYmImPx+RXKLq6jko/L07phmS9I02TyqkcNU20GCF/GgaWvc58hPtjxDX8lPpkdwc9sNh72V9k00S7ezog==}
    engines: {node: '>=8'}
    dev: false

  /cjs-module-lexer/1.2.2:
    resolution: {integrity: sha512-cOU9usZw8/dXIXKtwa8pM0OTJQuJkxMN6w30csNRUerHfeQ5R6U3kkU/FtJeIf3M202OHfY2U8ccInBG7/xogA==}
    dev: false

  /clean-css/4.2.4:
    resolution: {integrity: sha1-czv0brpOYHxokepXwkqYk1aDEXg=}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  /clean-stack/2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: false

  /cli-cursor/3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: false

  /cli-spinners/2.6.1:
    resolution: {integrity: sha1-rclU6+KBw3pjGb+kAebdJIj/tw0=}
    engines: {node: '>=6'}
    dev: false

  /cli-width/3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}
    dev: false

  /cliui/6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0
    dev: false

  /cliui/8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: false

  /clone/1.0.4:
    resolution: {integrity: sha1-2jCcwmPfFZlMaIypAheco8fNfH4=}
    engines: {node: '>=0.8'}
    dev: false

  /co/4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}
    dev: false

  /collect-v8-coverage/1.0.1:
    resolution: {integrity: sha512-iBPtljfCNcTKNAto0KEtDfZ3qzjJvqE3aTGZsbhjSBlorqpXJlaWWtPO35D+ZImoC3KWejX64o+yPGxhWSTzfg==}
    dev: false

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name/1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /colord/2.9.2:
    resolution: {integrity: sha512-Uqbg+J445nc1TKn4FoDPS6ZZqAvEDnwrH42yo8B40JSOgSLxMZ/gt3h4nmCtPLQeXhjJJkqBx7SCY35WnIixaQ==}
    dev: false

  /combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /commander/2.11.0:
    resolution: {integrity: sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ==}
    dev: false

  /commander/2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}
    dev: false

  /commander/6.2.1:
    resolution: {integrity: sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==}
    engines: {node: '>= 6'}
    dev: false

  /commondir/1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=}
    dev: false

  /compare-versions/3.6.0:
    resolution: {integrity: sha512-W6Af2Iw1z4CB7q4uU4hv646dW9GQuBM+YpC0UvUCWSD8w90SJjp+ujJuXaEMtAXBtSqGfMPuFOVn4/+FlaqfBA==}
    dev: true

  /concat-map/0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}
    dev: false

  /conf/9.0.2:
    resolution: {integrity: sha512-rLSiilO85qHgaTBIIHQpsv8z+NnVfZq3cKuYNCXN1AOqPzced0GWZEe/A517VldRLyQYXUMyV+vszavE2jSAqw==}
    engines: {node: '>=10'}
    dependencies:
      ajv: 7.2.4
      ajv-formats: 1.6.1
      atomically: 1.7.0
      debounce-fn: 4.0.0
      dot-prop: 6.0.1
      env-paths: 2.2.1
      json-schema-typed: 7.0.3
      make-dir: 3.1.0
      onetime: 5.1.2
      pkg-up: 3.1.0
      semver: 7.3.5
    dev: false

  /config-chain/1.1.13:
    resolution: {integrity: sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==}
    dependencies:
      ini: 1.3.8
      proto-list: 1.2.4
    dev: false

  /content-disposition/0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /content-type/1.0.4:
    resolution: {integrity: sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==}
    engines: {node: '>= 0.6'}
    dev: false

  /convert-source-map/1.8.0:
    resolution: {integrity: sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA==}
    dependencies:
      safe-buffer: 5.1.2
    dev: false

  /cookie-signature/1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=}
    dev: false

  /cookie/0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}
    dev: false

  /copy-anything/2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}
    dependencies:
      is-what: 3.14.1
    dev: false

  /core-js-compat/3.24.1:
    resolution: {integrity: sha512-XhdNAGeRnTpp8xbD+sR/HFDK9CbeeeqXT6TuofXh3urqEevzkWmLRgrVoykodsw8okqo2pu1BOmuCKrHx63zdw==}
    dependencies:
      browserslist: 4.21.3
      semver: 7.0.0
    dev: false

  /core-js/2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3 is no longer maintained and not recommended for usage due to the number of issues. Please, upgrade your dependencies to the actual version of core-js@3.
    requiresBuild: true
    dev: false

  /core-js/3.21.1:
    resolution: {integrity: sha512-FRq5b/VMrWlrmCzwRrpDYNxyHP9BcAZC+xHJaqTgIE5091ZV1NTmyh0sGOg5XqpnHvR0svdy0sv1gWA1zmhxig==}
    requiresBuild: true
    dev: false

  /core-util-is/1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=}
    dev: false

  /core-util-is/1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=}
    dev: false

  /cos-nodejs-sdk-v5/2.11.12:
    resolution: {integrity: sha512-XtSlcrwgcyO8K0LCwNmimtkBErC1yJ55cvZ7nWFWsT0c2AWBw8F/ftGvUhZIZhh7B2SlPdXsFZg+QOU7cwI2GQ==}
    engines: {node: '>= 6'}
    dependencies:
      conf: 9.0.2
      mime-types: 2.1.35
      request: 2.88.2
      xml2js: 0.4.23
    dev: false

  /cosmiconfig/7.0.1:
    resolution: {integrity: sha1-cU11ZSLKzoZ4Z8y0R0xdAbuuXW0=}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /create-cert/1.0.6:
    resolution: {integrity: sha1-ftAf/3+fDOpQCrpe/xGa9MjehPY=}
    dependencies:
      pem: 1.14.6
      pify: 3.0.0
    dev: false

  /create-test-server/3.0.1:
    resolution: {integrity: sha512-RRc+7LXSm+w3j9j3gJMVveD0Aa8uZCYhZtetpGltHZQg7ND9gW7m5jWk4OTSQgeD6PL+8gun8/CS1Pn3b5+M5A==}
    dependencies:
      body-parser: 1.20.1
      create-cert: 1.0.6
      express: 4.18.2
      pify: 3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /cross-spawn/7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: false

  /crypt/0.0.2:
    resolution: {integrity: sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=}
    dev: false

  /css-declaration-sorter/6.3.0_postcss@8.4.14:
    resolution: {integrity: sha512-OGT677UGHJTAVMRhPO+HJ4oKln3wkBTwtDFH0ojbqm+MJm6xuDMHp2nkhh/ThaBqq20IbraBQSWKfSLNHQO9Og==}
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: 8.4.14
    dev: false

  /css-select/4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: false

  /css-tree/1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  /css-what/6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: false

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /cssnano-preset-default/5.2.12_postcss@8.4.14:
    resolution: {integrity: sha512-OyCBTZi+PXgylz9HAA5kHyoYhfGcYdwFmyaJzWnzxuGRtnMw/kR6ilW9XzlzlRAtB6PLT/r+prYgkef7hngFew==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      css-declaration-sorter: 6.3.0_postcss@8.4.14
      cssnano-utils: 3.1.0_postcss@8.4.14
      postcss: 8.4.14
      postcss-calc: 8.2.4_postcss@8.4.14
      postcss-colormin: 5.3.0_postcss@8.4.14
      postcss-convert-values: 5.1.2_postcss@8.4.14
      postcss-discard-comments: 5.1.2_postcss@8.4.14
      postcss-discard-duplicates: 5.1.0_postcss@8.4.14
      postcss-discard-empty: 5.1.1_postcss@8.4.14
      postcss-discard-overridden: 5.1.0_postcss@8.4.14
      postcss-merge-longhand: 5.1.6_postcss@8.4.14
      postcss-merge-rules: 5.1.2_postcss@8.4.14
      postcss-minify-font-values: 5.1.0_postcss@8.4.14
      postcss-minify-gradients: 5.1.1_postcss@8.4.14
      postcss-minify-params: 5.1.3_postcss@8.4.14
      postcss-minify-selectors: 5.2.1_postcss@8.4.14
      postcss-normalize-charset: 5.1.0_postcss@8.4.14
      postcss-normalize-display-values: 5.1.0_postcss@8.4.14
      postcss-normalize-positions: 5.1.1_postcss@8.4.14
      postcss-normalize-repeat-style: 5.1.1_postcss@8.4.14
      postcss-normalize-string: 5.1.0_postcss@8.4.14
      postcss-normalize-timing-functions: 5.1.0_postcss@8.4.14
      postcss-normalize-unicode: 5.1.0_postcss@8.4.14
      postcss-normalize-url: 5.1.0_postcss@8.4.14
      postcss-normalize-whitespace: 5.1.1_postcss@8.4.14
      postcss-ordered-values: 5.1.3_postcss@8.4.14
      postcss-reduce-initial: 5.1.0_postcss@8.4.14
      postcss-reduce-transforms: 5.1.0_postcss@8.4.14
      postcss-svgo: 5.1.0_postcss@8.4.14
      postcss-unique-selectors: 5.1.1_postcss@8.4.14
    dev: false

  /cssnano-utils/3.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
    dev: false

  /cssnano/5.1.12_postcss@8.4.14:
    resolution: {integrity: sha512-TgvArbEZu0lk/dvg2ja+B7kYoD7BBCmn3+k58xD0qjrGHsFzXY/wKTo9M5egcUCabPol05e/PVoIu79s2JN4WQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-preset-default: 5.2.12_postcss@8.4.14
      lilconfig: 2.0.6
      postcss: 8.4.14
      yaml: 1.10.2
    dev: false

  /csso/4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: false

  /dashdash/1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0
    dev: false

  /debounce-fn/4.0.0:
    resolution: {integrity: sha512-8pYCQiL9Xdcg0UPSD3d+0KMlOjp+KGU5EPwYddgzQ7DATsg4fuUDjQtsYLmWjnk2obnNHgV3vE2Y4jejSOJVBQ==}
    engines: {node: '>=10'}
    dependencies:
      mimic-fn: 3.1.0
    dev: false

  /debug/2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: false

  /debug/3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: false
    optional: true

  /debug/4.3.3:
    resolution: {integrity: sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: false

  /debuglog/1.0.1:
    resolution: {integrity: sha1-qiT/uaw9+aI1GDfPstJ5NgzXhJI=}
    dev: false

  /decamelize/1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=}
    engines: {node: '>=0.10.0'}
    dev: false

  /dedent/0.7.0:
    resolution: {integrity: sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=}
    dev: false

  /deepmerge/4.2.2:
    resolution: {integrity: sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /defaults/1.0.3:
    resolution: {integrity: sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=}
    dependencies:
      clone: 1.0.4
    dev: false

  /define-properties/1.1.3:
    resolution: {integrity: sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      object-keys: 1.1.1
    dev: false

  /del/5.1.0:
    resolution: {integrity: sha512-wH9xOVHnczo9jN2IW68BabcecVPxacIA3g/7z6vhSU/4stOKQzeCRK0yD0A24WiAAUJmmVpWqrERcTxnLo3AnA==}
    engines: {node: '>=8'}
    dependencies:
      globby: 10.0.2
      graceful-fs: 4.2.9
      is-glob: 4.0.3
      is-path-cwd: 2.2.0
      is-path-inside: 3.0.3
      p-map: 3.0.0
      rimraf: 3.0.2
      slash: 3.0.0
    dev: false

  /delayed-stream/1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}
    dev: false

  /depd/2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}
    dev: false

  /destroy/1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: false

  /detect-indent/4.0.0:
    resolution: {integrity: sha1-920GQ1LN9Docts5hnE7jqUdd4gg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      repeating: 2.0.1
    dev: false

  /detect-newline/3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==}
    engines: {node: '>=8'}
    dev: false

  /dezalgo/1.0.4:
    resolution: {integrity: sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==}
    dependencies:
      asap: 2.0.6
      wrappy: 1.0.2
    dev: false

  /diff-sequences/28.1.1:
    resolution: {integrity: sha512-FU0iFaH/E23a+a718l8Qa/19bF9p06kgE0KipMOMadwa3SjnaElKzPaUC0vnibs6/B/9ni97s61mcejk8W1fQw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dev: false

  /dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: false

  /dom-serializer/1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.2.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: false

  /dom-walk/0.1.2:
    resolution: {integrity: sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==}
    dev: false

  /domelementtype/2.2.0:
    resolution: {integrity: sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A==}
    dev: false

  /domhandler/4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.2.0
    dev: false

  /domutils/2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.2.0
      domhandler: 4.3.1
    dev: false

  /dot-prop/6.0.1:
    resolution: {integrity: sha512-tE7ztYzXHIeyvc7N+hR3oi7FIbf/NIjVP9hmAt3yMXzrQ072/fpjGLx2GxNxGxUl5V73MEqYzioOMoVhGMJ5cA==}
    engines: {node: '>=10'}
    dependencies:
      is-obj: 2.0.0
    dev: false

  /dotenv/16.0.1:
    resolution: {integrity: sha512-1K6hR6wtk2FviQ4kEiSjFiH5rpzEVi8WW0x96aztHVMhEspNpc4DVOUTEHtEva5VThQ8IaBX1Pe4gSzpVVUsKQ==}
    engines: {node: '>=12'}
    dev: false

  /ecc-jsbn/0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2
    dev: false

  /ee-first/1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=}
    dev: false

  /electron-to-chromium/1.4.211:
    resolution: {integrity: sha512-BZSbMpyFQU0KBJ1JG26XGeFI3i4op+qOYGxftmZXFZoHkhLgsSv4DHDJfl8ogII3hIuzGt51PaZ195OVu0yJ9A==}
    dev: false

  /electron-to-chromium/1.4.71:
    resolution: {integrity: sha512-Hk61vXXKRb2cd3znPE9F+2pLWdIOmP7GjiTj45y6L3W/lO+hSnUSUhq+6lEaERWBdZOHbk2s3YV5c9xVl3boVw==}
    dev: false

  /emittery/0.10.2:
    resolution: {integrity: sha512-aITqOwnLanpHLNXZJENbOgjUBeHocD+xsSJmNrjovKBW5HbSpW3d1pEls7GFQPUWXiwG9+0P4GtHfEqC/4M0Iw==}
    engines: {node: '>=12'}
    dev: false

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: false

  /encodeurl/1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=}
    engines: {node: '>= 0.8'}
    dev: false

  /encoding/0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}
    dependencies:
      iconv-lite: 0.6.3
    dev: false

  /entities/2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}
    dev: false

  /env-paths/2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==}
    engines: {node: '>=6'}
    dev: false

  /errno/0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: false

  /error-ex/1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1

  /es6-promisify/6.1.1:
    resolution: {integrity: sha512-HBL8I3mIki5C1Cc9QjKUenHtnG0A5/xA8Q/AllRcfiwl2CZFXGK7ddBiCoRwAix4i2KxcQfjtIVcrVbB3vbmwg==}
    dev: false

  /escalade/3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}
    dev: false

  /escape-html/1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=}
    dev: false

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}

  /escape-string-regexp/2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}
    dev: false

  /estree-walker/0.6.1:
    resolution: {integrity: sha512-SqmZANLWS0mnatqbSfRP5g8OXZC12Fgg1IwNtLsyHDzJizORW4khDfjPqJZsemPWBB2uqykUah5YpQ6epsqC/w==}
    dev: false

  /estree-walker/1.0.1:
    resolution: {integrity: sha512-1fMXF3YP4pZZVozF8j/ZLfvnR8NSIljt56UhbZ5PeeDmmGHpgpdwQt7ITlGvYaQukCvuBRMLEiKiYC+oeIg4cg==}
    dev: false

  /estree-walker/2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}
    dev: false

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /etag/1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=}
    engines: {node: '>= 0.6'}
    dev: false

  /eventemitter3/4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: false

  /execa/5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: false

  /exif-parser/0.1.12:
    resolution: {integrity: sha1-WKnS1ywCwfbwKg70qRZicrd2CSI=}
    dev: false

  /exit/0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=}
    engines: {node: '>= 0.8.0'}
    dev: false

  /expect/28.1.3:
    resolution: {integrity: sha512-eEh0xn8HlsuOBxFgIss+2mX85VAS4Qy3OSkjV7rlBWljtA4oWH37glVGyOZSZvErDT/yBywZdPGwCXuTvSG85g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/expect-utils': 28.1.3
      jest-get-type: 28.0.2
      jest-matcher-utils: 28.1.3
      jest-message-util: 28.1.3
      jest-util: 28.1.3
    dev: false

  /express/4.18.2:
    resolution: {integrity: sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1
      content-disposition: 0.5.4
      content-type: 1.0.4
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0
      serve-static: 1.15.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    dev: false

  /external-editor/3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: false

  /extsprintf/1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=}
    engines: {'0': node >=0.6.0}
    dev: false

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: false

  /fast-glob/3.2.11:
    resolution: {integrity: sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.4
    dev: false

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: false

  /fastq/1.13.0:
    resolution: {integrity: sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=}
    dependencies:
      reusify: 1.0.4
    dev: false

  /fb-watchman/2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}
    dependencies:
      bser: 2.1.1
    dev: false

  /figures/3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: false

  /file-type/9.0.0:
    resolution: {integrity: sha512-Qe/5NJrgIOlwijpq3B7BEpzPFcgzggOTagZmkXQY4LA6bsXKTUstK7Wp12lEJ/mLKTpvIZxmIuRcLYWT6ov9lw==}
    engines: {node: '>=6'}
    dev: false

  /fill-range/7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: false

  /finalhandler/1.2.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /find-cache-dir/3.3.2:
    resolution: {integrity: sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: false

  /find-up/3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: false

  /find-up/4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0
    dev: false

  /find-up/5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /find-versions/4.0.0:
    resolution: {integrity: sha512-wgpWy002tA+wgmO27buH/9KzyEOQnKsG/R0yrcjPT9BOFm0zRBVQbZ95nRGXWMywS8YR5knRbpohio0bcJABxQ==}
    engines: {node: '>=10'}
    dependencies:
      semver-regex: 3.1.3
    dev: true

  /forever-agent/0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=}
    dev: false

  /form-data/2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /forwarded/0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}
    dev: false

  /fraction.js/4.2.0:
    resolution: {integrity: sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==}
    dev: false

  /fresh/0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=}
    engines: {node: '>= 0.6'}
    dev: false

  /fs-extra/10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.9
      jsonfile: 6.1.0
      universalify: 2.0.0
    dev: false

  /fs-extra/8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.9
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: false

  /fs.realpath/1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}
    dev: false

  /function-bind/1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}
    dev: false

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /get-caller-file/2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: false

  /get-intrinsic/1.1.1:
    resolution: {integrity: sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.2
    dev: false

  /get-package-type/0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}
    dev: false

  /get-proxy/2.1.0:
    resolution: {integrity: sha512-zmZIaQTWnNQb4R4fJUEp/FC51eZsc6EkErspy3xtIYStaq8EB/hDIWipxsal+E8rz0qD7f2sL/NA9Xee4RInJw==}
    engines: {node: '>=4'}
    dependencies:
      npm-conf: 1.1.3
    dev: false

  /get-stream/6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}
    dev: false

  /getpass/0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=}
    dependencies:
      assert-plus: 1.0.0
    dev: false

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: false

  /glob/7.1.2:
    resolution: {integrity: sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: false

  /glob/7.2.0:
    resolution: {integrity: sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: false

  /global/4.4.0:
    resolution: {integrity: sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==}
    dependencies:
      min-document: 2.19.0
      process: 0.11.10
    dev: false

  /globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}
    dev: false

  /globals/9.18.0:
    resolution: {integrity: sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /globby/10.0.2:
    resolution: {integrity: sha512-7dUi7RvCoT/xast/o/dLN53oqND4yk0nsHkhRgn9w65C4PofCLOoJ39iSOg+qVDdWQPIEj+eszMHQ+aLVwwQSg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/glob': 7.2.0
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.2.11
      glob: 7.2.0
      ignore: 5.2.0
      merge2: 1.4.1
      slash: 3.0.0
    dev: false

  /graceful-fs/4.2.9:
    resolution: {integrity: sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ==}
    dev: false

  /har-schema/2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=}
    engines: {node: '>=4'}
    dev: false

  /har-validator/5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0
    dev: false

  /has-ansi/2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /has-flag/3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  /has-symbols/1.0.2:
    resolution: {integrity: sha512-chXa79rL/UC2KlX17jo3vRGz0azaWEx5tGqZg5pO3NUyEJVB17dMruQlzCCOfUvElghKcm5194+BCRvi2Rv/Gw==}
    engines: {node: '>= 0.4'}
    dev: false

  /has/1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1
    dev: false

  /he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: false

  /home-or-tmp/2.0.0:
    resolution: {integrity: sha1-42w/LSyufXRqhX440Y1fMqeILbg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      os-homedir: 1.0.2
      os-tmpdir: 1.0.2
    dev: false

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}
    dev: false

  /html-escaper/2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}
    dev: false

  /html-minifier/4.0.0:
    resolution: {integrity: sha512-aoGxanpFPLg7MkIl/DDFYtb0iWz7jMFGqFhvEDZga6/4QTjneiD8I/NXL1x5aaoCp7FSIT6h/OhykDdPsbtMig==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      camel-case: 3.0.0
      clean-css: 4.2.4
      commander: 2.20.3
      he: 1.2.0
      param-case: 2.1.1
      relateurl: 0.2.7
      uglify-js: 3.16.3
    dev: false

  /http-errors/2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1
    dev: false

  /http-signature/1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.17.0
    dev: false

  /human-signals/2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}
    dev: false

  /husky/4.3.8:
    resolution: {integrity: sha512-LCqqsB0PzJQ/AlCgfrfzRe3e3+NvmefAdKQhRYpxS4u6clblBoDdzzvHi8fmxKRzvMxPY/1WZWzomPZww0Anow==}
    engines: {node: '>=10'}
    hasBin: true
    requiresBuild: true
    dependencies:
      chalk: 4.1.2
      ci-info: 2.0.0
      compare-versions: 3.6.0
      cosmiconfig: 7.0.1
      find-versions: 4.0.0
      opencollective-postinstall: 2.0.3
      pkg-dir: 5.0.0
      please-upgrade-node: 3.2.0
      slash: 3.0.0
      which-pm-runs: 1.0.0
    dev: true

  /iconv-lite/0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /iconv-lite/0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /ieee754/1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}
    dev: false

  /ignore/5.2.0:
    resolution: {integrity: sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==}
    engines: {node: '>= 4'}
    dev: false

  /immediate/3.0.6:
    resolution: {integrity: sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=}
    dev: false

  /immutable/4.1.0:
    resolution: {integrity: sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==}
    dev: false

  /import-fresh/3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /import-local/3.1.0:
    resolution: {integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0
    dev: false

  /imurmurhash/0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}
    dev: false

  /indent-string/4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: false

  /inflight/1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: false

  /inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}
    dev: false

  /ini/1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: false

  /inquirer/7.3.3:
    resolution: {integrity: sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: false

  /invariant/2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /ipaddr.js/1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}
    dev: false

  /is-arrayish/0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: false

  /is-buffer/1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}
    dev: false

  /is-builtin-module/3.2.0:
    resolution: {integrity: sha512-phDA4oSGt7vl1n5tJvTWooWWAsXLY+2xCnxNqvKhGEzujg+A43wPlPOyDg3C8XQHN+6k/JTQWJ/j0dQh/qr+Hw==}
    engines: {node: '>=6'}
    dependencies:
      builtin-modules: 3.3.0
    dev: false

  /is-core-module/2.8.1:
    resolution: {integrity: sha512-SdNCUs284hr40hFTFP6l0IfZ/RSrMXF3qgoRHd3/79unUTvrFO/JoXwkGm+5J/Oe3E/b5GsnG330uUNgRpu1PA==}
    dependencies:
      has: 1.0.3
    dev: false

  /is-extglob/2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-finite/1.1.0:
    resolution: {integrity: sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: false

  /is-function/1.0.2:
    resolution: {integrity: sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==}
    dev: false

  /is-generator-fn/2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}
    dev: false

  /is-glob/4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: false

  /is-interactive/1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}
    dev: false

  /is-module/1.0.0:
    resolution: {integrity: sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=}
    dev: false

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: false

  /is-obj/2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}
    dev: false

  /is-path-cwd/2.2.0:
    resolution: {integrity: sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==}
    engines: {node: '>=6'}
    dev: false

  /is-path-inside/3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}
    dev: false

  /is-reference/1.2.1:
    resolution: {integrity: sha512-U82MsXXiFIrjCK4otLT+o2NA2Cd2g5MLoOVXUZjIOhLurrRxpEXzI8O0KZHr3IjLvlAH1kTPYSuqer5T9ZVBKQ==}
    dependencies:
      '@types/estree': 1.0.0
    dev: false

  /is-stream/1.1.0:
    resolution: {integrity: sha1-EtSj3U5o4Lec6428hBc66A2RykQ=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-stream/2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: false

  /is-typedarray/1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=}
    dev: false

  /is-unicode-supported/0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}
    dev: false

  /is-what/3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}
    dev: false

  /isarray/1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=}
    dev: false

  /isexe/2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}
    dev: false

  /isomorphic-fetch/2.2.1:
    resolution: {integrity: sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=}
    dependencies:
      node-fetch: 1.7.3
      whatwg-fetch: 3.6.2
    dev: false

  /isstream/0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=}
    dev: false

  /istanbul-lib-coverage/3.2.0:
    resolution: {integrity: sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=}
    engines: {node: '>=8'}
    dev: false

  /istanbul-lib-instrument/5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/core': 7.17.5
      '@babel/parser': 7.18.11
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /istanbul-lib-report/3.0.0:
    resolution: {integrity: sha512-wcdi+uAKzfiGT2abPpKZ0hSU1rGQjUQnLvtY5MpQ7QCTahD3VODhcu4wcfY1YtkGaDD5yuydOLINXsfbus9ROw==}
    engines: {node: '>=8'}
    dependencies:
      istanbul-lib-coverage: 3.2.0
      make-dir: registry.npmmirror.com/make-dir/3.1.0
      supports-color: 7.2.0
    dev: false

  /istanbul-lib-source-maps/4.0.1:
    resolution: {integrity: sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=}
    engines: {node: '>=10'}
    dependencies:
      debug: 4.3.3
      istanbul-lib-coverage: 3.2.0
      source-map: registry.npmmirror.com/source-map/0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /istanbul-reports/3.1.5:
    resolution: {integrity: sha512-nUsEMa9pBt/NOHqbcbeJEgqIlY/K7rVWUX6Lql2orY5e9roQOthbR3vtY4zzf2orPELg80fnxxk9zUyPlgwD1w==}
    engines: {node: '>=8'}
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.0
    dev: false

  /jest-changed-files/28.1.3:
    resolution: {integrity: sha512-esaOfUWJXk2nfZt9SPyC8gA1kNfdKLkQWyzsMlqq8msYSlNKfmZxfRgZn4Cd4MGVUF+7v6dBs0d5TOAKa7iIiA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      execa: 5.1.1
      p-limit: 3.1.0
    dev: false

  /jest-circus/28.1.3:
    resolution: {integrity: sha512-cZ+eS5zc79MBwt+IhQhiEp0OeBddpc1n8MBo1nMB8A7oPMKEO+Sre+wHaLJexQUj9Ya/8NOBY0RESUgYjB6fow==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.3
      '@jest/expect': 28.1.3
      '@jest/test-result': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      chalk: 4.1.2
      co: 4.6.0
      dedent: 0.7.0
      is-generator-fn: 2.1.0
      jest-each: 28.1.3
      jest-matcher-utils: 28.1.3
      jest-message-util: 28.1.3
      jest-runtime: 28.1.3
      jest-snapshot: 28.1.3
      jest-util: 28.1.3
      p-limit: 3.1.0
      pretty-format: 28.1.3
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-cli/28.1.3:
    resolution: {integrity: sha512-roY3kvrv57Azn1yPgdTebPAXvdR2xfezaKKYzVxZ6It/5NCxzJym6tUI5P1zkdWhfUYkxEI9uZWcQdaFLo8mJQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/core': 28.1.3
      '@jest/test-result': 28.1.3
      '@jest/types': 28.1.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.9
      import-local: 3.1.0
      jest-config: 28.1.3
      jest-util: 28.1.3
      jest-validate: 28.1.3
      prompts: 2.4.2
      yargs: 17.6.2
    transitivePeerDependencies:
      - '@types/node'
      - supports-color
      - ts-node
    dev: false

  /jest-config/28.1.3:
    resolution: {integrity: sha512-MG3INjByJ0J4AsNBm7T3hsuxKQqFIiRo/AUqb1q9LRKI5UU6Aar9JHbr9Ivn1TVwfUD9KirRoM/T6u8XlcQPHQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true
    dependencies:
      '@babel/core': 7.17.5
      '@jest/test-sequencer': 28.1.3
      '@jest/types': 28.1.3
      babel-jest: 28.1.3_@babel+core@7.17.5
      chalk: 4.1.2
      ci-info: 3.7.0
      deepmerge: 4.2.2
      glob: 7.2.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-circus: 28.1.3
      jest-environment-node: 28.1.3
      jest-get-type: 28.0.2
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.3
      jest-runner: 28.1.3
      jest-util: 28.1.3
      jest-validate: 28.1.3
      micromatch: 4.0.4
      parse-json: 5.2.0
      pretty-format: 28.1.3
      slash: 3.0.0
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-config/28.1.3_@types+node@18.11.17:
    resolution: {integrity: sha512-MG3INjByJ0J4AsNBm7T3hsuxKQqFIiRo/AUqb1q9LRKI5UU6Aar9JHbr9Ivn1TVwfUD9KirRoM/T6u8XlcQPHQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true
    dependencies:
      '@babel/core': 7.17.5
      '@jest/test-sequencer': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      babel-jest: 28.1.3_@babel+core@7.17.5
      chalk: 4.1.2
      ci-info: 3.7.0
      deepmerge: 4.2.2
      glob: 7.2.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-circus: 28.1.3
      jest-environment-node: 28.1.3
      jest-get-type: 28.0.2
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.3
      jest-runner: 28.1.3
      jest-util: 28.1.3
      jest-validate: 28.1.3
      micromatch: 4.0.4
      parse-json: 5.2.0
      pretty-format: 28.1.3
      slash: 3.0.0
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-diff/28.1.3:
    resolution: {integrity: sha512-8RqP1B/OXzjjTWkqMX67iqgwBVJRgCyKD3L9nq+6ZqJMdvjE8RgHktqZ6jNrkdMT+dJuYNI3rhQpxaz7drJHfw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      chalk: 4.1.2
      diff-sequences: 28.1.1
      jest-get-type: 28.0.2
      pretty-format: 28.1.3
    dev: false

  /jest-docblock/28.1.1:
    resolution: {integrity: sha512-3wayBVNiOYx0cwAbl9rwm5kKFP8yHH3d/fkEaL02NPTkDojPtheGB7HZSFY4wzX+DxyrvhXz0KSCVksmCknCuA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      detect-newline: 3.1.0
    dev: false

  /jest-each/28.1.3:
    resolution: {integrity: sha512-arT1z4sg2yABU5uogObVPvSlSMQlDA48owx07BDPAiasW0yYpYHYOo4HHLz9q0BVzDVU4hILFjzJw0So9aCL/g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      chalk: 4.1.2
      jest-get-type: 28.0.2
      jest-util: 28.1.3
      pretty-format: 28.1.3
    dev: false

  /jest-environment-node/28.1.3:
    resolution: {integrity: sha512-ugP6XOhEpjAEhGYvp5Xj989ns5cB1K6ZdjBYuS30umT4CQEETaxSiPcZ/E1kFktX4GkrcM4qu07IIlDYX1gp+A==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.3
      '@jest/fake-timers': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      jest-mock: 28.1.3
      jest-util: 28.1.3
    dev: false

  /jest-get-type/28.0.2:
    resolution: {integrity: sha512-ioj2w9/DxSYHfOm5lJKCdcAmPJzQXmbM/Url3rhlghrPvT3tt+7a/+oXc9azkKmLvoiXjtV83bEWqi+vs5nlPA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dev: false

  /jest-haste-map/28.1.3:
    resolution: {integrity: sha512-3S+RQWDXccXDKSWnkHa/dPwt+2qwA8CJzR61w3FoYCvoo3Pn8tvGcysmMF0Bj0EX5RYvAI2EIvC57OmotfdtKA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      '@types/graceful-fs': 4.1.5
      '@types/node': 18.11.17
      anymatch: 3.1.2
      fb-watchman: 2.0.2
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-regex-util: 28.0.2
      jest-util: 28.1.3
      jest-worker: 28.1.3
      micromatch: 4.0.4
      walker: 1.0.8
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: false

  /jest-leak-detector/28.1.3:
    resolution: {integrity: sha512-WFVJhnQsiKtDEo5lG2mM0v40QWnBM+zMdHHyJs8AWZ7J0QZJS59MsyKeJHWhpBZBH32S48FOVvGyOFT1h0DlqA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      jest-get-type: 28.0.2
      pretty-format: 28.1.3
    dev: false

  /jest-matcher-utils/28.1.3:
    resolution: {integrity: sha512-kQeJ7qHemKfbzKoGjHHrRKH6atgxMk8Enkk2iPQ3XwO6oE/KYD8lMYOziCkeSB9G4adPM4nR1DE8Tf5JeWH6Bw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      chalk: 4.1.2
      jest-diff: 28.1.3
      jest-get-type: 28.0.2
      pretty-format: 28.1.3
    dev: false

  /jest-message-util/28.1.3:
    resolution: {integrity: sha512-PFdn9Iewbt575zKPf1286Ht9EPoJmYT7P0kY+RibeYZ2XtOr53pDLEFoTWXbd1h4JiGiWpTBC84fc8xMXQMb7g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@jest/types': 28.1.3
      '@types/stack-utils': 2.0.1
      chalk: 4.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      micromatch: 4.0.4
      pretty-format: 28.1.3
      slash: 3.0.0
      stack-utils: 2.0.6
    dev: false

  /jest-mock/28.1.3:
    resolution: {integrity: sha512-o3J2jr6dMMWYVH4Lh/NKmDXdosrsJgi4AviS8oXLujcjpCMBb1FMsblDnOXKZKfSiHLxYub1eS0IHuRXsio9eA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
    dev: false

  /jest-pnp-resolver/1.2.3_jest-resolve@28.1.3:
    resolution: {integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true
    dependencies:
      jest-resolve: 28.1.3
    dev: false

  /jest-regex-util/28.0.2:
    resolution: {integrity: sha512-4s0IgyNIy0y9FK+cjoVYoxamT7Zeo7MhzqRGx7YDYmaQn1wucY9rotiGkBzzcMXTtjrCAP/f7f+E0F7+fxPNdw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dev: false

  /jest-resolve-dependencies/28.1.3:
    resolution: {integrity: sha512-qa0QO2Q0XzQoNPouMbCc7Bvtsem8eQgVPNkwn9LnS+R2n8DaVDPL/U1gngC0LTl1RYXJU0uJa2BMC2DbTfFrHA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      jest-regex-util: 28.0.2
      jest-snapshot: 28.1.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-resolve/28.1.3:
    resolution: {integrity: sha512-Z1W3tTjE6QaNI90qo/BJpfnvpxtaFTFw5CDgwpyE/Kz8U/06N1Hjf4ia9quUhCh39qIGWF1ZuxFiBiJQwSEYKQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      chalk: 4.1.2
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-haste-map: 28.1.3
      jest-pnp-resolver: 1.2.3_jest-resolve@28.1.3
      jest-util: 28.1.3
      jest-validate: 28.1.3
      resolve: 1.22.0
      resolve.exports: 1.1.0
      slash: 3.0.0
    dev: false

  /jest-runner/28.1.3:
    resolution: {integrity: sha512-GkMw4D/0USd62OVO0oEgjn23TM+YJa2U2Wu5zz9xsQB1MxWKDOlrnykPxnMsN0tnJllfLPinHTka61u0QhaxBA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/console': 28.1.3
      '@jest/environment': 28.1.3
      '@jest/test-result': 28.1.3
      '@jest/transform': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      chalk: 4.1.2
      emittery: 0.10.2
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-docblock: 28.1.1
      jest-environment-node: 28.1.3
      jest-haste-map: 28.1.3
      jest-leak-detector: 28.1.3
      jest-message-util: 28.1.3
      jest-resolve: 28.1.3
      jest-runtime: 28.1.3
      jest-util: 28.1.3
      jest-watcher: 28.1.3
      jest-worker: 28.1.3
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-runtime/28.1.3:
    resolution: {integrity: sha512-NU+881ScBQQLc1JHG5eJGU7Ui3kLKrmwCPPtYsJtBykixrM2OhVQlpMmFWJjMyDfdkGgBMNjXCGB/ebzsgNGQw==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/environment': 28.1.3
      '@jest/fake-timers': 28.1.3
      '@jest/globals': 28.1.3
      '@jest/source-map': 28.1.2
      '@jest/test-result': 28.1.3
      '@jest/transform': 28.1.3
      '@jest/types': 28.1.3
      chalk: 4.1.2
      cjs-module-lexer: 1.2.2
      collect-v8-coverage: 1.0.1
      execa: 5.1.1
      glob: 7.2.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-haste-map: 28.1.3
      jest-message-util: 28.1.3
      jest-mock: 28.1.3
      jest-regex-util: 28.0.2
      jest-resolve: 28.1.3
      jest-snapshot: 28.1.3
      jest-util: 28.1.3
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-snapshot/28.1.3:
    resolution: {integrity: sha512-4lzMgtiNlc3DU/8lZfmqxN3AYD6GGLbl+72rdBpXvcV+whX7mDrREzkPdp2RnmfIiWBg1YbuFSkXduF2JcafJg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@babel/core': 7.17.5
      '@babel/generator': 7.18.10
      '@babel/plugin-syntax-typescript': 7.18.6_@babel+core@7.17.5
      '@babel/traverse': 7.18.11
      '@babel/types': 7.18.10
      '@jest/expect-utils': 28.1.3
      '@jest/transform': 28.1.3
      '@jest/types': 28.1.3
      '@types/babel__traverse': 7.18.3
      '@types/prettier': 2.7.2
      babel-preset-current-node-syntax: 1.0.1_@babel+core@7.17.5
      chalk: 4.1.2
      expect: 28.1.3
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      jest-diff: 28.1.3
      jest-get-type: 28.0.2
      jest-haste-map: 28.1.3
      jest-matcher-utils: 28.1.3
      jest-message-util: 28.1.3
      jest-util: 28.1.3
      natural-compare: 1.4.0
      pretty-format: 28.1.3
      semver: 7.3.5
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jest-util/28.1.3:
    resolution: {integrity: sha512-XdqfpHwpcSRko/C35uLYFM2emRAltIIKZiJ9eAmhjsj0CqZMa0p1ib0R5fWIqGhn1a103DebTbpqIaP1qCQ6tQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      chalk: 4.1.2
      ci-info: 3.7.0
      graceful-fs: 4.2.9
      picomatch: 2.3.1
    dev: false

  /jest-validate/28.1.3:
    resolution: {integrity: sha512-SZbOGBWEsaTxBGCOpsRWlXlvNkvTkY0XxRfh7zYmvd8uL5Qzyg0CHAXiXKROflh801quA6+/DsT4ODDthOC/OA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/types': 28.1.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 28.0.2
      leven: 3.1.0
      pretty-format: 28.1.3
    dev: false

  /jest-watcher/28.1.3:
    resolution: {integrity: sha512-t4qcqj9hze+jviFPUN3YAtAEeFnr/azITXQEMARf5cMwKY2SMBRnCQTXLixTl20OR6mLh9KLMrgVJgJISym+1g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/test-result': 28.1.3
      '@jest/types': 28.1.3
      '@types/node': 18.11.17
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.10.2
      jest-util: 28.1.3
      string-length: 4.0.2
    dev: false

  /jest-worker/26.6.2:
    resolution: {integrity: sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 18.11.17
      merge-stream: 2.0.0
      supports-color: 7.2.0
    dev: false

  /jest-worker/28.1.3:
    resolution: {integrity: sha512-CqRA220YV/6jCo8VWvAt1KKx6eek1VIHMPeLEbpcfSfkEeWyBNppynM/o6q+Wmw+sOhos2ml34wZbSX3G13//g==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@types/node': 18.11.17
      merge-stream: 2.0.0
      supports-color: 8.1.1
    dev: false

  /jest/28.1.3:
    resolution: {integrity: sha512-N4GT5on8UkZgH0O5LUavMRV1EDEhNTL0KEfRmDIeZHSV7p2XgLoY9t9VDUgL6o+yfdgYHVxuz81G8oB9VG5uyA==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true
    dependencies:
      '@jest/core': 28.1.3
      '@jest/types': 28.1.3
      import-local: 3.1.0
      jest-cli: 28.1.3
    transitivePeerDependencies:
      - '@types/node'
      - supports-color
      - ts-node
    dev: false

  /jimp/0.9.8:
    resolution: {integrity: sha512-DHN4apKMwLIvD/TKO9tFfPuankNuVK98vCwHm/Jv9z5cJnrd38xhi+4I7IAGmDU3jIDlrEVhzTkFH1Ymv5yTQQ==}
    dependencies:
      '@babel/runtime': 7.17.2
      '@jimp/custom': 0.9.8
      '@jimp/plugins': 0.9.8_@jimp+custom@0.9.8
      '@jimp/types': 0.9.8_@jimp+custom@0.9.8
      core-js: 3.21.1
      regenerator-runtime: 0.13.9
    dev: false

  /jpeg-js/0.3.7:
    resolution: {integrity: sha512-9IXdWudL61npZjvLuVe/ktHiA41iE8qFyLB+4VDTblEsWBzeg8WQTlktdUK4CdncUqtUgUg0bbOmTE2bKBKaBQ==}
    dev: false

  /js-yaml/3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: registry.npmmirror.com/esprima/4.0.1
    dev: false

  /jsbn/0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=}
    dev: false

  /jsesc/0.5.0:
    resolution: {integrity: sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=}
    hasBin: true
    dev: false

  /jsesc/1.3.0:
    resolution: {integrity: sha1-RsP+yMGJKxKwgz25vHYiF226s0s=}
    hasBin: true
    dev: false

  /jsesc/2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: false

  /json-schema-traverse/1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: false

  /json-schema-typed/7.0.3:
    resolution: {integrity: sha512-7DE8mpG+/fVw+dTpjbxnx47TaMnDfOI1jwft9g1VybltZCduyRQPJPvc+zzKY9WPHxhPWczyFuYa6I8Mw4iU5A==}
    dev: false

  /json-schema/0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=}
    dev: false

  /json-stringify-safe/5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=}
    dev: false

  /json5/0.5.1:
    resolution: {integrity: sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=}
    hasBin: true
    dev: false

  /json5/2.2.0:
    resolution: {integrity: sha512-f+8cldu7X/y7RAJurMEJmdoKXGB/X550w2Nr3tTbezL6RwEE/iMcm+tZnXeoZtKuOq6ft8+CqzEkrIgx1fPoQA==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      minimist: 1.2.5
    dev: false

  /json5/2.2.2:
    resolution: {integrity: sha512-46Tk9JiOL2z7ytNQWFLpj99RZkVgeHf87yGQKsIkaPz1qSH9UczKH1rO7K3wgRselo0tYMUNfecYpm/p1vC7tQ==}
    engines: {node: '>=6'}
    hasBin: true
    dev: false

  /jsonfile/4.0.0:
    resolution: {integrity: sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=}
    optionalDependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
    dev: false

  /jsonfile/6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
    dev: false

  /jsonschema/1.4.1:
    resolution: {integrity: sha512-S6cATIPVv1z0IlxdN+zUk5EPjkGCdnhN4wVSBlvoUO1tOLJootbo9CquNJmbIh4yikWHiUedhRYrNPn1arpEmQ==}
    dev: false

  /jsprim/1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0
    dev: false

  /jszip/3.10.1:
    resolution: {integrity: sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==}
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.7
      setimmediate: 1.0.5
    dev: false

  /kleur/3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}
    dev: false

  /less/4.1.3:
    resolution: {integrity: sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.4.0
    optionalDependencies:
      errno: registry.npmmirror.com/errno/0.1.8
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      image-size: registry.npmmirror.com/image-size/0.5.5
      make-dir: registry.npmmirror.com/make-dir/2.1.0
      mime: registry.npmmirror.com/mime/1.6.0
      needle: registry.npmmirror.com/needle/3.1.0
      source-map: registry.npmmirror.com/source-map/0.6.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /leven/3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}
    dev: false

  /licia/1.37.0:
    resolution: {integrity: sha512-jX49+WmzikOPGNrcy/giS23HCI8Pb7RF585Ei5d7oWF4WMelaZWv4odqQNdT0jtHkoUxqSvPz67Jvyq06xamUA==}
    dev: false

  /lie/3.3.0:
    resolution: {integrity: sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==}
    dependencies:
      immediate: 3.0.6
    dev: false

  /lilconfig/2.0.6:
    resolution: {integrity: sha512-9JROoBW7pobfsx+Sq2JsASvCo6Pfo6WWoUW79HuB1BCoBXD4PLWJPqDF6fNj67pqBYTbAHkE57M1kS/+L1neOg==}
    engines: {node: '>=10'}
    dev: false

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}

  /load-bmfont/1.4.1:
    resolution: {integrity: sha512-8UyQoYmdRDy81Brz6aLAUhfZLwr5zV0L3taTQ4hju7m6biuwiWiJXjPhBJxbUQJA8PrkvJ/7Enqmwk2sM14soA==}
    dependencies:
      buffer-equal: 0.0.1
      mime: registry.npmmirror.com/mime/1.6.0
      parse-bmfont-ascii: 1.0.6
      parse-bmfont-binary: 1.0.6
      parse-bmfont-xml: 1.1.4
      phin: 2.9.3
      xhr: 2.6.0
      xtend: 4.0.2
    dev: false

  /locate-path/3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: false

  /locate-path/5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0
    dev: false

  /locate-path/6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash.memoize/4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=}
    dev: false

  /lodash.uniq/4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=}
    dev: false

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /log-symbols/4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0
    dev: false

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: registry.npmmirror.com/js-tokens/4.0.0
    dev: false

  /lru-cache/6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: false

  /magic-string/0.25.9:
    resolution: {integrity: sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==}
    dependencies:
      sourcemap-codec: 1.4.8
    dev: false

  /make-dir/3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.0
    dev: false

  /make-error/1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}
    dev: false

  /makeerror/1.0.12:
    resolution: {integrity: sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=}
    dependencies:
      tmpl: 1.0.5
    dev: false

  /md5/2.3.0:
    resolution: {integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==}
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6
    dev: false

  /mdn-data/2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}
    dev: false

  /media-typer/0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=}
    engines: {node: '>= 0.6'}
    dev: false

  /memory-fs/0.5.0:
    resolution: {integrity: sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.7
    dev: false

  /merge-descriptors/1.0.1:
    resolution: {integrity: sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=}
    dev: false

  /merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: false

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: false

  /methods/1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=}
    engines: {node: '>= 0.6'}
    dev: false

  /micromatch/4.0.4:
    resolution: {integrity: sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1
    dev: false

  /mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: false

  /mimic-fn/3.1.0:
    resolution: {integrity: sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==}
    engines: {node: '>=8'}
    dev: false

  /min-document/2.19.0:
    resolution: {integrity: sha1-e9KC4/WELtKVu3SM3Z8f+iyCRoU=}
    dependencies:
      dom-walk: 0.1.2
    dev: false

  /minimatch/3.0.4:
    resolution: {integrity: sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==}
    dependencies:
      brace-expansion: 1.1.11
    dev: false

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11
    dev: false

  /minimist/1.2.5:
    resolution: {integrity: sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==}
    dev: false

  /miniprogram-ci/1.8.35:
    resolution: {integrity: sha512-Pzv9I5SR+yVGztwoz9/3CesEBlb1jgbVv8AJB7QLzZiYHOU+NebwaRKfMumarJAEHzMVFOJmKaQA8bXDFprLtA==}
    hasBin: true
    dependencies:
      '@babel/core': 7.12.3
      '@babel/generator': 7.17.10
      '@babel/helper-module-imports': 7.12.1
      '@babel/helpers': 7.12.1
      '@babel/parser': 7.17.10
      '@babel/plugin-proposal-class-properties': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-decorators': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-do-expressions': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-export-default-from': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-function-bind': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-function-sent': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-pipeline-operator': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-private-methods': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-proposal-throw-expressions': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-transform-modules-commonjs': 7.18.6_@babel+core@7.12.3
      '@babel/plugin-transform-runtime': 7.12.1_@babel+core@7.12.3
      '@babel/plugin-transform-typescript': 7.18.10_@babel+core@7.12.3
      '@babel/preset-env': 7.12.1_@babel+core@7.12.3
      '@babel/runtime': 7.12.1
      '@babel/template': 7.16.7
      '@babel/traverse': 7.17.10
      '@vue/reactivity': 3.0.5
      acorn: 6.4.2
      autoprefixer: 10.4.8_postcss@8.4.14
      babel-code-frame: 6.26.0
      babel-core: 6.26.0
      babel-preset-es2015: 6.24.1
      babel-preset-stage-0: 6.24.1
      chokidar: 3.5.3
      cos-nodejs-sdk-v5: 2.11.12
      cssnano: 5.1.12_postcss@8.4.14
      eventemitter3: 4.0.7
      fs-extra: 8.1.0
      get-proxy: 2.1.0
      glob: 7.1.2
      html-minifier: 4.0.0
      jimp: 0.9.8
      jsonschema: 1.4.1
      jszip: 3.10.1
      less: 4.1.3
      licia: 1.37.0
      lodash: 4.17.21
      memory-fs: 0.5.0
      minimatch: 3.0.4
      moment-timezone: 0.5.34
      postcss: 8.4.14
      qrcode-reader: 1.0.4
      qrcode-terminal: 0.12.0
      read-package-tree: 5.2.1
      request: 2.88.2
      rimraf: 3.0.2
      sass: 1.54.3
      source-map: 0.6.1
      string-hash-64: 1.0.3
      terser: 4.8.0
      tslib: 1.10.0
      uglify-js: 3.0.27
      wxml-minifier: 0.0.1
      yargs: 15.4.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mkdirp/0.5.5:
    resolution: {integrity: sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==}
    hasBin: true
    dependencies:
      minimist: 1.2.5
    dev: false

  /moment-timezone/0.5.34:
    resolution: {integrity: sha512-3zAEHh2hKUs3EXLESx/wsgw6IQdusOT8Bxm3D9UrHPQR7zlMmzwybC8zHEM1tQ4LJwP7fcxrWr8tuBg05fFCbg==}
    dependencies:
      moment: 2.29.4
    dev: false

  /moment/2.29.4:
    resolution: {integrity: sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==}
    dev: false

  /ms/2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}
    dev: false

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: false

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: false

  /mute-stream/0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}
    dev: false

  /nanoid/3.3.4:
    resolution: {integrity: sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: false

  /natural-compare/1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}
    dev: false

  /negotiator/0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}
    dev: false

  /node-fetch/1.7.3:
    resolution: {integrity: sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==}
    dependencies:
      encoding: 0.1.13
      is-stream: 1.1.0
    dev: false

  /node-int64/0.4.0:
    resolution: {integrity: sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=}
    dev: false

  /node-releases/2.0.2:
    resolution: {integrity: sha512-XxYDdcQ6eKqp/YjI+tb2C5WM2LgjnZrfYg4vgQt49EK268b6gYCHsBLrK2qvJo4FmCtqmKezb0WZFK4fkrZNsg==}
    dev: false

  /node-releases/2.0.6:
    resolution: {integrity: sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==}
    dev: false

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.0
      semver: 5.7.1
      validate-npm-package-license: 3.0.4
    dev: false

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /normalize-range/0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=}
    engines: {node: '>=0.10.0'}
    dev: false

  /normalize-url/6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==}
    engines: {node: '>=10'}
    dev: false

  /npm-conf/1.1.3:
    resolution: {integrity: sha512-Yic4bZHJOt9RCFbRP3GgpqhScOY4HH3V2P8yBj6CeYq118Qr+BLXqT2JvpJ00mryLESpgOxf5XlFv4ZjXxLScw==}
    engines: {node: '>=4'}
    dependencies:
      config-chain: 1.1.13
      pify: 3.0.0
    dev: false

  /npm-normalize-package-bin/1.0.1:
    resolution: {integrity: sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA==}
    dev: false

  /npm-run-path/4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: false

  /nth-check/2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: false

  /oauth-sign/0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}
    dev: false

  /object-inspect/1.12.0:
    resolution: {integrity: sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==}
    dev: false

  /object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: false

  /object.assign/4.1.2:
    resolution: {integrity: sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.3
      has-symbols: 1.0.2
      object-keys: 1.1.1
    dev: false

  /omggif/1.0.10:
    resolution: {integrity: sha512-LMJTtvgc/nugXj0Vcrrs68Mn2D1r0zf630VNtqtpI1FEO7e+O9FP4gqs9AcnBaSEeoHIPm28u6qgPR0oyEpGSw==}
    dev: false

  /on-finished/2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: false

  /once/1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}
    dependencies:
      wrappy: 1.0.2
    dev: false

  /onetime/5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: false

  /opencollective-postinstall/2.0.3:
    resolution: {integrity: sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==}
    hasBin: true
    dev: true

  /ora/5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==}
    engines: {node: '>=10'}
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.6.1
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1
    dev: false

  /os-homedir/1.0.2:
    resolution: {integrity: sha1-/7xJiDNuDoM94MFox+8VISGqf7M=}
    engines: {node: '>=0.10.0'}
    dev: false

  /os-tmpdir/1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=}
    engines: {node: '>=0.10.0'}
    dev: false

  /p-limit/2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: false

  /p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0

  /p-locate/3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: false

  /p-locate/4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0
    dev: false

  /p-locate/5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-map/3.0.0:
    resolution: {integrity: sha512-d3qXVTF/s+W+CdJ5A29wywV2n8CQQYahlgz2bFiA+4eVNJbHJodPZ+/gXwPGh0bOqA+j8S+6+ckmvLGPk1QpxQ==}
    engines: {node: '>=8'}
    dependencies:
      aggregate-error: 3.1.0
    dev: false

  /p-try/2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}
    dev: false

  /pako/1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}
    dev: false

  /param-case/2.1.1:
    resolution: {integrity: sha1-35T9jPZTHs915r75oIWPvHK+Ikc=}
    dependencies:
      no-case: registry.npmmirror.com/no-case/2.3.2
    dev: false

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-bmfont-ascii/1.0.6:
    resolution: {integrity: sha1-Eaw8P/WPfCAgqyJ2kHkQjU36AoU=}
    dev: false

  /parse-bmfont-binary/1.0.6:
    resolution: {integrity: sha1-0Di0dtPp3Z2x4RoLDlOiJ5K2kAY=}
    dev: false

  /parse-bmfont-xml/1.1.4:
    resolution: {integrity: sha512-bjnliEOmGv3y1aMEfREMBJ9tfL3WR0i0CKPj61DnSLaoxWR3nLrsQrEbCId/8rF4NyRF0cCqisSVXyQYWM+mCQ==}
    dependencies:
      xml-parse-from-string: 1.0.1
      xml2js: 0.4.23
    dev: false

  /parse-headers/2.0.5:
    resolution: {integrity: sha512-ft3iAoLOB/MlwbNXgzy43SWGP6sQki2jQvAyBg/zDFAgr9bfNWZIUj42Kw2eJIl8kEi4PbgE6U1Zau/HwI75HA==}
    dev: false

  /parse-json/5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.16.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  /parse-node-version/1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}
    dev: false

  /parseurl/1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /path-exists/3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=}
    engines: {node: '>=4'}
    dev: false

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}
    dev: false

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: false

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: false

  /path-to-regexp/0.1.7:
    resolution: {integrity: sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=}
    dev: false

  /path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  /pem/1.14.6:
    resolution: {integrity: sha512-I5GKUer2PPv5qzUfxaZ6IGRkhp+357Kyv2t1JJg9vP8hGGI13qU34N2QupmggbpIZGPuudH0jn8KU5hjFpPk3g==}
    engines: {node: '>=6.0.0'}
    dependencies:
      es6-promisify: 6.1.1
      md5: 2.3.0
      os-tmpdir: 1.0.2
      which: 2.0.2
    dev: false

  /performance-now/2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=}
    dev: false

  /phin/2.9.3:
    resolution: {integrity: sha512-CzFr90qM24ju5f88quFC/6qohjC144rehe5n6DH900lgXmUe86+xCKc10ev56gRKC4/BkHUoG4uSiQgBiIXwDA==}
    dev: false

  /picocolors/1.0.0:
    resolution: {integrity: sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=}
    dev: false

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}
    dev: false

  /pify/3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=}
    engines: {node: '>=4'}
    dev: false

  /pify/4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    dev: false
    optional: true

  /pirates/4.0.5:
    resolution: {integrity: sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==}
    engines: {node: '>= 6'}
    dev: false

  /pixelmatch/4.0.2:
    resolution: {integrity: sha1-j0fc7FARtHe2fbA8JDvB8wheiFQ=}
    hasBin: true
    dependencies:
      pngjs: 3.4.0
    dev: false

  /pkg-dir/4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
    dev: false

  /pkg-dir/5.0.0:
    resolution: {integrity: sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==}
    engines: {node: '>=10'}
    dependencies:
      find-up: 5.0.0
    dev: true

  /pkg-up/3.1.0:
    resolution: {integrity: sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 3.0.0
    dev: false

  /please-upgrade-node/3.2.0:
    resolution: {integrity: sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==}
    dependencies:
      semver-compare: 1.0.0
    dev: true

  /pngjs/3.4.0:
    resolution: {integrity: sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==}
    engines: {node: '>=4.0.0'}
    dev: false

  /postcss-calc/8.2.4_postcss@8.4.14:
    resolution: {integrity: sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==}
    peerDependencies:
      postcss: ^8.2.2
    dependencies:
      postcss: 8.4.14
      postcss-selector-parser: 6.0.9
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-colormin/5.3.0_postcss@8.4.14:
    resolution: {integrity: sha512-WdDO4gOFG2Z8n4P8TWBpshnL3JpmNmJwdnfP2gbk2qBA8PWwOYcmjmI/t3CmMeL72a7Hkd+x/Mg9O2/0rD54Pg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      caniuse-api: 3.0.0
      colord: 2.9.2
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-convert-values/5.1.2_postcss@8.4.14:
    resolution: {integrity: sha512-c6Hzc4GAv95B7suy4udszX9Zy4ETyMCgFPUDtWjdFTKH1SE9eFY/jEpHSwTH1QPuwxHpWslhckUQWbNRM4ho5g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-discard-comments/5.1.2_postcss@8.4.14:
    resolution: {integrity: sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
    dev: false

  /postcss-discard-duplicates/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
    dev: false

  /postcss-discard-empty/5.1.1_postcss@8.4.14:
    resolution: {integrity: sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
    dev: false

  /postcss-discard-overridden/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
    dev: false

  /postcss-merge-longhand/5.1.6_postcss@8.4.14:
    resolution: {integrity: sha512-6C/UGF/3T5OE2CEbOuX7iNO63dnvqhGZeUnKkDeifebY0XqkkvrctYSZurpNE902LDf2yKwwPFgotnfSoPhQiw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.0_postcss@8.4.14
    dev: false

  /postcss-merge-rules/5.1.2_postcss@8.4.14:
    resolution: {integrity: sha512-zKMUlnw+zYCWoPN6yhPjtcEdlJaMUZ0WyVcxTAmw3lkkN/NDMRkOkiuctQEoWAOvH7twaxUUdvBWl0d4+hifRQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0_postcss@8.4.14
      postcss: 8.4.14
      postcss-selector-parser: 6.0.9
    dev: false

  /postcss-minify-font-values/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-gradients/5.1.1_postcss@8.4.14:
    resolution: {integrity: sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      colord: 2.9.2
      cssnano-utils: 3.1.0_postcss@8.4.14
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-params/5.1.3_postcss@8.4.14:
    resolution: {integrity: sha512-bkzpWcjykkqIujNL+EVEPOlLYi/eZ050oImVtHU7b4lFS82jPnsCb44gvC6pxaNt38Els3jWYDHTjHKf0koTgg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      cssnano-utils: 3.1.0_postcss@8.4.14
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-selectors/5.2.1_postcss@8.4.14:
    resolution: {integrity: sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-selector-parser: 6.0.9
    dev: false

  /postcss-normalize-charset/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
    dev: false

  /postcss-normalize-display-values/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-positions/5.1.1_postcss@8.4.14:
    resolution: {integrity: sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-repeat-style/5.1.1_postcss@8.4.14:
    resolution: {integrity: sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-string/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-timing-functions/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-unicode/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-J6M3MizAAZ2dOdSjy2caayJLQT8E8K9XjLce8AUQMwOrCvjCHv24aLC/Lps1R1ylOfol5VIDMaM/Lo9NGlk1SQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-url/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-whitespace/5.1.1_postcss@8.4.14:
    resolution: {integrity: sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-ordered-values/5.1.3_postcss@8.4.14:
    resolution: {integrity: sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-utils: 3.1.0_postcss@8.4.14
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-reduce-initial/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-5OgTUviz0aeH6MtBjHfbr57tml13PuedK/Ecg8szzd4XRMbYxH4572JFG067z+FqBIf6Zp/d+0581glkvvWMFw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      caniuse-api: 3.0.0
      postcss: 8.4.14
    dev: false

  /postcss-reduce-transforms/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-selector-parser/6.0.9:
    resolution: {integrity: sha512-UO3SgnZOVTwu4kyLR22UQ1xZh086RyNZppb7lLAKBFK8a32ttG5i87Y/P3+2bRSjZNyJ1B7hfFNo273tKe9YxQ==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: false

  /postcss-svgo/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-value-parser: 4.2.0
      svgo: 2.8.0
    dev: false

  /postcss-unique-selectors/5.1.1_postcss@8.4.14:
    resolution: {integrity: sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.14
      postcss-selector-parser: 6.0.9
    dev: false

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: false

  /postcss/8.4.14:
    resolution: {integrity: sha512-E398TUmfAYFPBSdzgeieK2Y1+1cpdxJx8yXbK/m57nRhKSmk1GB2tO4lbLBtlkfPQTDKfe4Xqv1ASWPpayPEig==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.4
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: false

  /pretty-format/28.1.3:
    resolution: {integrity: sha512-8gFb/To0OmxHR9+ZTb14Df2vNxdGCX8g1xWGUTqUw5TiZvcQf5sHKObd5UcPyLLyowNwDAMTF3XWOG1B6mxl1Q==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@jest/schemas': 28.1.3
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 18.2.0
    dev: false

  /private/0.1.8:
    resolution: {integrity: sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==}
    engines: {node: '>= 0.6'}
    dev: false

  /process-nextick-args/2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}
    dev: false

  /process/0.11.10:
    resolution: {integrity: sha1-czIwDoQBYb2j5podHZGn1LwW8YI=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /prompts/2.4.2:
    resolution: {integrity: sha1-e1fnOzpIAprRDr1E90sBcipMsGk=}
    engines: {node: '>= 6'}
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5
    dev: false

  /proto-list/1.2.4:
    resolution: {integrity: sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk=}
    dev: false

  /proxy-addr/2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1
    dev: false

  /prr/1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=}
    dev: false

  /psl/1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}
    dev: false

  /punycode/2.1.1:
    resolution: {integrity: sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==}
    engines: {node: '>=6'}
    dev: false

  /qrcode-reader/1.0.4:
    resolution: {integrity: sha512-rRjALGNh9zVqvweg1j5OKIQKNsw3bLC+7qwlnead5K/9cb1cEIAGkwikt/09U0K+2IDWGD9CC6SP7tHAjUeqvQ==}
    dev: false

  /qrcode-terminal/0.12.0:
    resolution: {integrity: sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==}
    hasBin: true
    dev: false

  /qs/6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4
    dev: false

  /qs/6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}
    dev: false

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: false

  /randombytes/2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /range-parser/1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}
    dev: false

  /raw-body/2.5.1:
    resolution: {integrity: sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0
    dev: false

  /react-is/18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}
    dev: false

  /read-package-json/2.1.2:
    resolution: {integrity: sha512-D1KmuLQr6ZSJS0tW8hf3WGpRlwszJOXZ3E8Yd/DNRaM5d+1wVRZdHlpGBLAuovjr28LbWvjpWkBHMxpRGGjzNA==}
    dependencies:
      glob: 7.2.0
      json-parse-even-better-errors: 2.3.1
      normalize-package-data: 2.5.0
      npm-normalize-package-bin: 1.0.1
    dev: false

  /read-package-tree/5.2.1:
    resolution: {integrity: sha512-2CNoRoh95LxY47LvqrehIAfUVda2JbuFE/HaGYs42bNrGG+ojbw1h3zOcPcQ+1GQ3+rkzNndZn85u1XyZ3UsIA==}
    dependencies:
      debuglog: 1.0.1
      dezalgo: 1.0.4
      once: 1.4.0
      read-package-json: 2.1.2
      readdir-scoped-modules: 1.1.0
    dev: false

  /readable-stream/2.3.7:
    resolution: {integrity: sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2
    dev: false

  /readable-stream/3.6.0:
    resolution: {integrity: sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: false

  /readdir-scoped-modules/1.1.0:
    resolution: {integrity: sha512-asaikDeqAQg7JifRsZn1NJZXo9E+VwlyCfbkZhwyISinqk5zNS6266HS5kah6P0SaQKGF6SkNnZVHUzHFYxYDw==}
    dependencies:
      debuglog: 1.0.1
      dezalgo: 1.0.4
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.9
      once: 1.4.0
    dev: false

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: false

  /regenerate-unicode-properties/10.0.1:
    resolution: {integrity: sha512-vn5DU6yg6h8hP/2OkQo3K7uVILvY4iu0oI4t3HFa81UPkhGJwkRwM10JEc3upjdhHjs/k8GJY1sRBhk5sr69Bw==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: false

  /regenerate/1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}
    dev: false

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: false

  /regenerator-runtime/0.13.9:
    resolution: {integrity: sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==}
    dev: false

  /regenerator-transform/0.10.1:
    resolution: {integrity: sha512-PJepbvDbuK1xgIgnau7Y90cwaAmO/LCLMI2mPvaXq2heGMR3aWW5/BQvYrhJ8jgmQjXewXvBjzfqKcVOmhjZ6Q==}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      private: 0.1.8
    dev: false

  /regenerator-transform/0.15.0:
    resolution: {integrity: sha512-LsrGtPmbYg19bcPHwdtmXwbW+TqNvtY4riE3P83foeHRroMbH6/2ddFBfab3t7kbzc7v7p4wbkIecHImqt0QNg==}
    dependencies:
      '@babel/runtime': 7.17.2
    dev: false

  /regexpu-core/2.0.0:
    resolution: {integrity: sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=}
    dependencies:
      regenerate: 1.4.2
      regjsgen: 0.2.0
      regjsparser: 0.1.5
    dev: false

  /regexpu-core/5.1.0:
    resolution: {integrity: sha512-bb6hk+xWd2PEOkj5It46A16zFMs2mv86Iwpdu94la4S3sJ7C973h2dHpYKwIBGaWSO7cIRJ+UX0IeMaWcO4qwA==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.0.1
      regjsgen: 0.6.0
      regjsparser: 0.8.4
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.0.0
    dev: false

  /regjsgen/0.2.0:
    resolution: {integrity: sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=}
    dev: false

  /regjsgen/0.6.0:
    resolution: {integrity: sha1-g0FMU1Sv19ZiexavXxD0HE5xgI0=}
    dev: false

  /regjsparser/0.1.5:
    resolution: {integrity: sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: false

  /regjsparser/0.8.4:
    resolution: {integrity: sha512-J3LABycON/VNEu3abOviqGHuB/LOtOQj8SKmfP9anY5GfAVw/SPjwzSjxGjbZXIxbGfqTHtJw58C2Li/WkStmA==}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: false

  /relateurl/0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=}
    engines: {node: '>= 0.10'}
    dev: false

  /repeating/2.0.1:
    resolution: {integrity: sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-finite: 1.1.0
    dev: false

  /request/2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.11.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0
    dev: false

  /require-directory/2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}
    dev: false

  /require-from-string/2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /require-main-filename/2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}
    dev: false

  /resolve-cwd/3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}
    dependencies:
      resolve-from: 5.0.0
    dev: false

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-from/5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}
    dev: false

  /resolve.exports/1.1.0:
    resolution: {integrity: sha1-XOhCuUsFFGwOAwdphdHQ5+SMkMk=}
    engines: {node: '>=10'}
    dev: false

  /resolve/1.22.0:
    resolution: {integrity: sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==}
    hasBin: true
    dependencies:
      is-core-module: 2.8.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: false

  /restore-cursor/3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.6
    dev: false

  /reusify/1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: false

  /rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    dependencies:
      glob: 7.2.0
    dev: false

  /rollup-plugin-analyzer/4.0.0:
    resolution: {integrity: sha512-LL9GEt3bkXp6Wa19SNR5MWcvHNMvuTFYg+eYBZN2OIFhSWN+pEJUQXEKu5BsOeABob3x9PDaLKW7w5iOJnsESQ==}
    engines: {node: '>=8.0.0'}
    dev: false

  /rollup-plugin-commonjs/10.1.0_rollup@2.79.1:
    resolution: {integrity: sha512-jlXbjZSQg8EIeAAvepNwhJj++qJWNJw1Cl0YnOqKtP5Djx+fFGkp3WRh+W0ASCaFG5w1jhmzDxgu3SJuVxPF4Q==}
    deprecated: This package has been deprecated and is no longer maintained. Please use @rollup/plugin-commonjs.
    peerDependencies:
      rollup: '>=1.12.0'
    dependencies:
      estree-walker: 0.6.1
      is-reference: 1.2.1
      magic-string: 0.25.9
      resolve: 1.22.0
      rollup: 2.79.1
      rollup-pluginutils: 2.8.2
    dev: false

  /rollup-plugin-delete/2.0.0:
    resolution: {integrity: sha512-/VpLMtDy+8wwRlDANuYmDa9ss/knGsAgrDhM+tEwB1npHwNu4DYNmDfUL55csse/GHs9Q+SMT/rw9uiaZ3pnzA==}
    engines: {node: '>=10'}
    dependencies:
      del: 5.1.0
    dev: false

  /rollup-plugin-terser/7.0.2_rollup@2.79.1:
    resolution: {integrity: sha512-w3iIaU4OxcF52UUXiZNsNeuXIMDvFrr+ZXK6bFZ0Q60qyVfq4uLptoS4bbq3paG3x216eQllFZX7zt6TIImguQ==}
    peerDependencies:
      rollup: ^2.0.0
    dependencies:
      '@babel/code-frame': 7.18.6
      jest-worker: 26.6.2
      rollup: 2.79.1
      serialize-javascript: 4.0.0
      terser: 5.16.1
    dev: false

  /rollup-plugin-typescript2/0.32.1_rollup@2.79.1+typescript@4.9.4:
    resolution: {integrity: sha512-RanO8bp1WbeMv0bVlgcbsFNCn+Y3rX7wF97SQLDxf0fMLsg0B/QFF005t4AsGUcDgF3aKJHoqt4JF2xVaABeKw==}
    peerDependencies:
      rollup: '>=1.26.3'
      typescript: '>=2.4.0'
    dependencies:
      '@rollup/pluginutils': 4.2.1
      find-cache-dir: 3.3.2
      fs-extra: 10.1.0
      resolve: 1.22.0
      rollup: 2.79.1
      tslib: 2.4.0
      typescript: 4.9.4
    dev: false

  /rollup-pluginutils/2.8.2:
    resolution: {integrity: sha512-EEp9NhnUkwY8aif6bxgovPHMoMoNr2FulJziTndpt5H9RdwC47GSGuII9XxpSdzVGM0GWrNPHV6ie1LTNJPaLQ==}
    dependencies:
      estree-walker: 0.6.1
    dev: false

  /rollup/2.79.1:
    resolution: {integrity: sha512-uKxbd0IhMZOhjAiD5oAFp7BqvkA4Dv47qpOCtaNvng4HBwdbWtdOh8f5nZNuk2rp51PMGk3bzfWu5oayNEuYnw==}
    engines: {node: '>=10.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: false

  /run-async/2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}
    dev: false

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: false

  /rxjs/6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1
    dev: false

  /safe-buffer/5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}
    dev: false

  /safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}
    dev: false

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: false

  /sass/1.54.3:
    resolution: {integrity: sha512-fLodey5Qd41Pxp/Tk7Al97sViYwF/TazRc5t6E65O7JOk4XF8pzwIW7CvCxYVOfJFFI/1x5+elDyBIixrp+zrw==}
    engines: {node: '>=12.0.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.3
      immutable: 4.1.0
      source-map-js: 1.0.2
    dev: false

  /sax/1.2.4:
    resolution: {integrity: sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==}
    dev: false

  /semver-compare/1.0.0:
    resolution: {integrity: sha1-De4hahyUGrN+nvsXiPavxf9VN/w=}
    dev: true

  /semver-regex/3.1.3:
    resolution: {integrity: sha1-srzG+X9jJp8oaZTil+IptiRdDcM=}
    engines: {node: '>=8'}
    dev: true

  /semver/5.7.1:
    resolution: {integrity: sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==}
    hasBin: true
    dev: false

  /semver/6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true
    dev: false

  /semver/7.0.0:
    resolution: {integrity: sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==}
    hasBin: true
    dev: false

  /semver/7.3.5:
    resolution: {integrity: sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: false

  /send/0.18.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: registry.npmmirror.com/mime/1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /serialize-javascript/4.0.0:
    resolution: {integrity: sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /serve-static/1.15.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /set-blocking/2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=}
    dev: false

  /setimmediate/1.0.5:
    resolution: {integrity: sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=}
    dev: false

  /setprototypeof/1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}
    dev: false

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: false

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: false

  /side-channel/1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.1
      object-inspect: 1.12.0
    dev: false

  /signal-exit/3.0.6:
    resolution: {integrity: sha1-JOYwxLDwP+pEaivSmeYrSmyo0K8=}
    dev: false

  /signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: false

  /sisteransi/1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}
    dev: false

  /slash/1.0.0:
    resolution: {integrity: sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=}
    engines: {node: '>=0.10.0'}
    dev: false

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  /source-map-js/1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map-support/0.4.18:
    resolution: {integrity: sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA==}
    dependencies:
      source-map: registry.npmmirror.com/source-map/0.5.7
    dev: false

  /source-map-support/0.5.13:
    resolution: {integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  /source-map-support/0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}
    dependencies:
      buffer-from: 1.1.2
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /sourcemap-codec/1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==}
    dev: false

  /spdx-correct/3.1.1:
    resolution: {integrity: sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.11
    dev: false

  /spdx-exceptions/2.3.0:
    resolution: {integrity: sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==}
    dev: false

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.11
    dev: false

  /spdx-license-ids/3.0.11:
    resolution: {integrity: sha1-UMDYxAoU7Bv0Sbrmmg6kaFqdn5U=}
    dev: false

  /sprintf-js/1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=}
    dev: false

  /sshpk/1.17.0:
    resolution: {integrity: sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5
    dev: false

  /stable/0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    dev: false

  /stack-utils/2.0.6:
    resolution: {integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==}
    engines: {node: '>=10'}
    dependencies:
      escape-string-regexp: 2.0.0
    dev: false

  /statuses/2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /string-hash-64/1.0.3:
    resolution: {integrity: sha512-D5OKWKvDhyVWWn2x5Y9b+37NUllks34q1dCDhk/vYcso9fmhs+Tl3KR/gE4v5UNj2UA35cnX4KdVVGkG1deKqw==}
    dev: false

  /string-length/4.0.2:
    resolution: {integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==}
    engines: {node: '>=10'}
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1
    dev: false

  /string-width/4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: false

  /string_decoder/1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2
    dev: false

  /string_decoder/1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /strip-ansi/3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /strip-ansi/6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: false

  /strip-bom/4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}
    dev: false

  /strip-final-newline/2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: false

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: false

  /stylehacks/5.1.0_postcss@8.4.14:
    resolution: {integrity: sha512-SzLmvHQTrIWfSgljkQCw2++C9+Ne91d/6Sp92I8c5uHTcy/PgeHamwITIbBW9wnFTY/3ZfSXR9HIL6Ikqmcu6Q==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.3
      postcss: 8.4.14
      postcss-selector-parser: 6.0.9
    dev: false

  /supports-color/2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=}
    engines: {node: '>=0.8.0'}
    dev: false

  /supports-color/5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0

  /supports-color/8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: false

  /supports-hyperlinks/2.2.0:
    resolution: {integrity: sha512-6sXEzV5+I5j8Bmq9/vUphGRM/RJNT9SCURJLjwfOg51heRtguGWDzcaBlgAzKhQa0EVNpPEKzQuBwZ8S8WaCeQ==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0
    dev: false

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: false

  /svgo/2.8.0:
    resolution: {integrity: sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: registry.npmmirror.com/commander/7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.0.0
      stable: 0.1.8
    dev: false

  /terminal-link/2.1.1:
    resolution: {integrity: sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==}
    engines: {node: '>=8'}
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.2.0
    dev: false

  /terser/4.8.0:
    resolution: {integrity: sha512-EAPipTNeWsb/3wLPeup1tVPaXfIaU68xMnVdPafIL1TV05OhASArYyIfFvnvJCNrR2NIOvDVNNTFRa+Re2MWyw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      acorn: registry.npmmirror.com/acorn/8.7.0
      commander: 2.20.3
      source-map: registry.npmmirror.com/source-map/0.6.1
      source-map-support: 0.5.21
    dev: false

  /terser/5.16.1:
    resolution: {integrity: sha512-xvQfyfA1ayT0qdK47zskQgRZeWLoOQ8JQ6mIgRGVNwZKdQMU+5FkCBjmv4QjcrTzyZquRw2FVtlJSRUmMKQslw==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.2
      acorn: registry.npmmirror.com/acorn/8.7.0
      commander: registry.npmmirror.com/commander/2.20.3
      source-map-support: 0.5.21
    dev: false

  /test-exclude/6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.0
      minimatch: 3.1.2
    dev: false

  /through/2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}
    dev: false

  /timm/1.7.1:
    resolution: {integrity: sha512-IjZc9KIotudix8bMaBW6QvMuq64BrJWFs1+4V0lXwWGQZwH+LnX87doAYhem4caOEusRP9/g6jVDQmZ8XOk1nw==}
    dev: false

  /tinycolor2/1.4.2:
    resolution: {integrity: sha512-vJhccZPs965sV/L2sU4oRQVAos0pQXwsvTLkWYdqJ+a8Q5kPFzJTuOFwy7UniPli44NKQGAglksjvOcpo95aZA==}
    dev: false

  /tmp/0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: false

  /tmpl/1.0.5:
    resolution: {integrity: sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=}
    dev: false

  /to-fast-properties/1.0.3:
    resolution: {integrity: sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=}
    engines: {node: '>=0.10.0'}
    dev: false

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=}
    engines: {node: '>=4'}
    dev: false

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: false

  /toidentifier/1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=}
    engines: {node: '>=0.6'}
    dev: false

  /tough-cookie/2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.9.0
      punycode: 2.1.1
    dev: false

  /trim-right/1.0.1:
    resolution: {integrity: sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=}
    engines: {node: '>=0.10.0'}
    dev: false

  /ts-jest/28.0.8_jest@28.1.3+typescript@4.9.4:
    resolution: {integrity: sha512-5FaG0lXmRPzApix8oFG8RKjAz4ehtm8yMKOTy5HX3fY6W8kmvOrmcY0hKDElW52FJov+clhUbrKAqofnj4mXTg==}
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    hasBin: true
    peerDependencies:
      '@babel/core': '>=7.0.0-beta.0 <8'
      '@jest/types': ^28.0.0
      babel-jest: ^28.0.0
      esbuild: '*'
      jest: ^28.0.0
      typescript: '>=4.3'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      '@jest/types':
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true
    dependencies:
      bs-logger: 0.2.6
      fast-json-stable-stringify: 2.1.0
      jest: 28.1.3
      jest-util: 28.1.3
      json5: 2.2.2
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      semver: 7.3.5
      typescript: 4.9.4
      yargs-parser: 21.1.1
    dev: false

  /tslib/1.10.0:
    resolution: {integrity: sha512-qOebF53frne81cf0S9B41ByenJ3/IuH8yJKngAX35CmiZySA0khhkovshKK+jGCaMnVomla7gVlIcc3EvKPbTQ==}
    dev: false

  /tslib/1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}
    dev: false

  /tslib/2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}
    dev: false

  /tunnel-agent/0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /tweetnacl/0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=}
    dev: false

  /type-detect/4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}
    dev: false

  /type-fest/0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: false

  /type-is/1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35
    dev: false

  /typescript/4.9.4:
    resolution: {integrity: sha512-Uz+dTXYzxXXbsFpM86Wh3dKCxrQqUcVMxwU54orwlJjOpO3ao8L7j5lH+dWfTwgCwIuM9GQ2kvVotzYJMXTBZg==}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: false

  /uglify-js/3.0.27:
    resolution: {integrity: sha512-HD8CmxPXUI62v5tweiulMcP/apAtx1DXGcNZkhKQZyC+MTrTsoCBb8yPAwVrbvpgw3EpRU76bRe6axjIiCYcQg==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dependencies:
      commander: 2.11.0
      source-map: registry.npmmirror.com/source-map/0.5.7
    dev: false

  /uglify-js/3.16.3:
    resolution: {integrity: sha512-uVbFqx9vvLhQg0iBaau9Z75AxWJ8tqM9AV890dIZCLApF4rTcyHwmAvLeEdYRs+BzYWu8Iw81F79ah0EfTXbaw==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dev: false

  /umi-request/1.4.0:
    resolution: {integrity: sha1-7Q5U5H8EPSvgbmkUd/CJA4P53Yo=}
    dependencies:
      isomorphic-fetch: 2.2.1
      qs: 6.11.0
    dev: false

  /unicode-canonical-property-names-ecmascript/2.0.0:
    resolution: {integrity: sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=}
    engines: {node: '>=4'}
    dev: false

  /unicode-match-property-ecmascript/2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.0.0
    dev: false

  /unicode-match-property-value-ecmascript/2.0.0:
    resolution: {integrity: sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ=}
    engines: {node: '>=4'}
    dev: false

  /unicode-property-aliases-ecmascript/2.0.0:
    resolution: {integrity: sha1-CjbLmlhcT2q9Ua0d7dsoXBZSl8g=}
    engines: {node: '>=4'}
    dev: false

  /universalify/0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}
    dev: false

  /universalify/2.0.0:
    resolution: {integrity: sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==}
    engines: {node: '>= 10.0.0'}
    dev: false

  /unpipe/1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=}
    engines: {node: '>= 0.8'}
    dev: false

  /update-browserslist-db/1.0.5_browserslist@4.21.3:
    resolution: {integrity: sha512-dteFFpCyvuDdr9S/ff1ISkKt/9YZxKjI9WlRR99c180GaztJtRa/fn18FdxGVKVsnPY7/a/FDN68mcvUmP4U7Q==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.3
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: false

  /uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.1.1
    dev: false

  /utif/2.0.1:
    resolution: {integrity: sha512-Z/S1fNKCicQTf375lIP9G8Sa1H/phcysstNrrSdZKj1f9g58J4NMgb5IgiEZN9/nLMPDwF0W7hdOe9Qq2IYoLg==}
    dependencies:
      pako: 1.0.11
    dev: false

  /util-deprecate/1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=}
    dev: false

  /utils-merge/1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=}
    engines: {node: '>= 0.4.0'}
    dev: false

  /uuid/3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    hasBin: true
    dev: false

  /v8-to-istanbul/9.0.1:
    resolution: {integrity: sha512-74Y4LqY74kLE6IFyIjPtkSTWzUZmj8tdHT9Ii/26dvQ6K9Dl2NbEfj0XgU2sHCtKgt5VupqhlO/5aWuqS+IY1w==}
    engines: {node: '>=10.12.0'}
    dependencies:
      '@jridgewell/trace-mapping': 0.3.14
      '@types/istanbul-lib-coverage': 2.0.4
      convert-source-map: 1.8.0
    dev: false

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.1.1
      spdx-expression-parse: 3.0.1
    dev: false

  /vary/1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=}
    engines: {node: '>= 0.8'}
    dev: false

  /verror/1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0
    dev: false

  /walker/1.0.8:
    resolution: {integrity: sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=}
    dependencies:
      makeerror: 1.0.12
    dev: false

  /wcwidth/1.0.1:
    resolution: {integrity: sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=}
    dependencies:
      defaults: 1.0.3
    dev: false

  /whatwg-fetch/3.6.2:
    resolution: {integrity: sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA==}
    dev: false

  /which-module/2.0.0:
    resolution: {integrity: sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=}
    dev: false

  /which-pm-runs/1.0.0:
    resolution: {integrity: sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs=}
    dev: true

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: false

  /wrap-ansi/6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: false

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: false

  /wrappy/1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}
    dev: false

  /write-file-atomic/4.0.2:
    resolution: {integrity: sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7
    dev: false

  /wxml-minifier/0.0.1:
    resolution: {integrity: sha512-g8ZS4fyLdyRIcExnevKTnAFxbtYlAPKBGFO1DXOcsJfmppQWjH2xe2Ff6rRQ2ubYAWalaNjMYpkAl6hurhqkHg==}
    dependencies:
      '@leejim/wxml-parser': 0.1.6
    dev: false

  /xhr/2.6.0:
    resolution: {integrity: sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA==}
    dependencies:
      global: 4.4.0
      is-function: 1.0.2
      parse-headers: 2.0.5
      xtend: 4.0.2
    dev: false

  /xml-parse-from-string/1.0.1:
    resolution: {integrity: sha1-qQKekp09vN7RafPG4oI42VpdWig=}
    dev: false

  /xml2js/0.4.23:
    resolution: {integrity: sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==}
    engines: {node: '>=4.0.0'}
    dependencies:
      sax: 1.2.4
      xmlbuilder: 11.0.1
    dev: false

  /xmlbuilder/11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}
    dev: false

  /xtend/4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: false

  /y18n/4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}
    dev: false

  /y18n/5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: false

  /yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: false

  /yaml/1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  /yargs-parser/18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: false

  /yargs-parser/21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}
    dev: false

  /yargs/15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.0
      y18n: 4.0.3
      yargs-parser: 18.1.3
    dev: false

  /yargs/17.6.2:
    resolution: {integrity: sha512-1/9UrdHjDZc0eOU0HxOHoS78C69UD3JRMvzlJ7S79S2nTaWRA/whGCTV8o9e/N/1Va9YIV7Q4sOxD8VV4pCWOw==}
    engines: {node: '>=12'}
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1
    dev: false

  /yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  registry.npmmirror.com/acorn/5.7.4:
    resolution: {integrity: sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn/-/acorn-5.7.4.tgz}
    name: acorn
    version: 5.7.4
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  registry.npmmirror.com/acorn/8.7.0:
    resolution: {integrity: sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn/-/acorn-8.7.0.tgz}
    name: acorn
    version: 8.7.0
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  registry.npmmirror.com/art-template/4.13.2:
    resolution: {integrity: sha512-04ws5k+ndA5DghfheY4c8F1304XJKeTcaXqZCLpxFkNMSkaR3ChW1pX2i9d3sEEOZuLy7de8lFriRaik1jEeOQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/art-template/-/art-template-4.13.2.tgz}
    name: art-template
    version: 4.13.2
    engines: {node: '>= 1.0.0'}
    dependencies:
      acorn: registry.npmmirror.com/acorn/5.7.4
      escodegen: registry.npmmirror.com/escodegen/1.14.3
      estraverse: registry.npmmirror.com/estraverse/4.3.0
      html-minifier: registry.npmmirror.com/html-minifier/3.5.21
      is-keyword-js: registry.npmmirror.com/is-keyword-js/1.0.3
      js-tokens: registry.npmmirror.com/js-tokens/3.0.2
      merge-source-map: registry.npmmirror.com/merge-source-map/1.1.0
      source-map: registry.npmmirror.com/source-map/0.5.7
    dev: false

  registry.npmmirror.com/camel-case/3.0.0:
    resolution: {integrity: sha512-+MbKztAYHXPr1jNTSKQF52VpcFjwY5RkR7fxksV8Doo4KAYc5Fl4UJRgthBbTmEx8C54DqahhbLJkDwjI3PI/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/camel-case/-/camel-case-3.0.0.tgz}
    name: camel-case
    version: 3.0.0
    dependencies:
      no-case: registry.npmmirror.com/no-case/2.3.2
      upper-case: registry.npmmirror.com/upper-case/1.1.3
    dev: false

  registry.npmmirror.com/clean-css/4.2.4:
    resolution: {integrity: sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/clean-css/-/clean-css-4.2.4.tgz}
    name: clean-css
    version: 4.2.4
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  registry.npmmirror.com/commander/2.17.1:
    resolution: {integrity: sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-2.17.1.tgz}
    name: commander
    version: 2.17.1
    dev: false

  registry.npmmirror.com/commander/2.19.0:
    resolution: {integrity: sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-2.19.0.tgz}
    name: commander
    version: 2.19.0
    dev: false

  registry.npmmirror.com/commander/2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz}
    name: commander
    version: 2.20.3
    dev: false

  registry.npmmirror.com/commander/7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz}
    name: commander
    version: 7.2.0
    engines: {node: '>= 10'}
    dev: false

  registry.npmmirror.com/deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz}
    name: deep-is
    version: 0.1.4
    dev: false

  registry.npmmirror.com/errno/0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/errno/-/errno-0.1.8.tgz}
    name: errno
    version: 0.1.8
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: false
    optional: true

  registry.npmmirror.com/escodegen/1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escodegen/-/escodegen-1.14.3.tgz}
    name: escodegen
    version: 1.14.3
    engines: {node: '>=4.0'}
    hasBin: true
    dependencies:
      esprima: registry.npmmirror.com/esprima/4.0.1
      estraverse: registry.npmmirror.com/estraverse/4.3.0
      esutils: registry.npmmirror.com/esutils/2.0.3
      optionator: registry.npmmirror.com/optionator/0.8.3
    optionalDependencies:
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  registry.npmmirror.com/esprima/4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz}
    name: esprima
    version: 4.0.1
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  registry.npmmirror.com/estraverse/4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz}
    name: estraverse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dev: false

  registry.npmmirror.com/esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz}
    name: esutils
    version: 2.0.3
    engines: {node: '>=0.10.0'}
    dev: false

  registry.npmmirror.com/fast-levenshtein/2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}
    name: fast-levenshtein
    version: 2.0.6
    dev: false

  registry.npmmirror.com/fsevents/2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz}
    name: fsevents
    version: 2.3.2
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  registry.npmmirror.com/graceful-fs/4.2.9:
    resolution: {integrity: sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.9.tgz}
    name: graceful-fs
    version: 4.2.9
    dev: false

  registry.npmmirror.com/he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/he/-/he-1.2.0.tgz}
    name: he
    version: 1.2.0
    hasBin: true
    dev: false

  registry.npmmirror.com/html-minifier/3.5.21:
    resolution: {integrity: sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/html-minifier/-/html-minifier-3.5.21.tgz}
    name: html-minifier
    version: 3.5.21
    engines: {node: '>=4'}
    hasBin: true
    dependencies:
      camel-case: registry.npmmirror.com/camel-case/3.0.0
      clean-css: registry.npmmirror.com/clean-css/4.2.4
      commander: registry.npmmirror.com/commander/2.17.1
      he: registry.npmmirror.com/he/1.2.0
      param-case: registry.npmmirror.com/param-case/2.1.1
      relateurl: registry.npmmirror.com/relateurl/0.2.7
      uglify-js: registry.npmmirror.com/uglify-js/3.4.10
    dev: false

  registry.npmmirror.com/image-size/0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz}
    name: image-size
    version: 0.5.5
    engines: {node: '>=0.10.0'}
    hasBin: true
    requiresBuild: true
    dev: false
    optional: true

  registry.npmmirror.com/is-keyword-js/1.0.3:
    resolution: {integrity: sha512-EW8wNCNvomPa/jsH1g0DmLfPakkRCRTcTML1v1fZMLiVCvQ/1YB+tKsRzShBiWQhqrYCi5a+WsepA4Z8TA9iaA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-keyword-js/-/is-keyword-js-1.0.3.tgz}
    name: is-keyword-js
    version: 1.0.3
    engines: {node: '>=0.10.0'}
    dev: false

  registry.npmmirror.com/js-tokens/3.0.2:
    resolution: {integrity: sha512-RjTcuD4xjtthQkaWH7dFlH85L+QaVtSoOyGdZ3g6HFhS9dFNDfLyqgm2NFe2X6cQpeFmt0452FJjFG5UameExg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-tokens/-/js-tokens-3.0.2.tgz}
    name: js-tokens
    version: 3.0.2
    dev: false

  registry.npmmirror.com/js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz}
    name: js-tokens
    version: 4.0.0

  registry.npmmirror.com/levn/0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/levn/-/levn-0.3.0.tgz}
    name: levn
    version: 0.3.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmmirror.com/prelude-ls/1.1.2
      type-check: registry.npmmirror.com/type-check/0.3.2
    dev: false

  registry.npmmirror.com/lower-case/1.1.4:
    resolution: {integrity: sha512-2Fgx1Ycm599x+WGpIYwJOvsjmXFzTSc34IwDWALRA/8AopUKAVPwfJ+h5+f85BCp0PWmmJcWzEpxOpoXycMpdA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lower-case/-/lower-case-1.1.4.tgz}
    name: lower-case
    version: 1.1.4
    dev: false

  registry.npmmirror.com/make-dir/2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz}
    name: make-dir
    version: 2.1.0
    engines: {node: '>=6'}
    requiresBuild: true
    dependencies:
      pify: 4.0.1
      semver: 5.7.1
    dev: false
    optional: true

  registry.npmmirror.com/make-dir/3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz}
    name: make-dir
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.0
    dev: false

  registry.npmmirror.com/merge-source-map/1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge-source-map/-/merge-source-map-1.1.0.tgz}
    name: merge-source-map
    version: 1.1.0
    dependencies:
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  registry.npmmirror.com/mime/1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz}
    name: mime
    version: 1.6.0
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  registry.npmmirror.com/needle/3.1.0:
    resolution: {integrity: sha512-gCE9weDhjVGCRqS8dwDR/D3GTAeyXLXuqp7I8EzH6DllZGXSUyxuqqLh+YX9rMAWaaTFyVAg6rHGL25dqvczKw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/needle/-/needle-3.1.0.tgz}
    name: needle
    version: 3.1.0
    engines: {node: '>= 4.4.x'}
    hasBin: true
    requiresBuild: true
    dependencies:
      debug: 3.2.7
      iconv-lite: 0.6.3
      sax: 1.2.4
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  registry.npmmirror.com/no-case/2.3.2:
    resolution: {integrity: sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/no-case/-/no-case-2.3.2.tgz}
    name: no-case
    version: 2.3.2
    dependencies:
      lower-case: registry.npmmirror.com/lower-case/1.1.4
    dev: false

  registry.npmmirror.com/optionator/0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/optionator/-/optionator-0.8.3.tgz}
    name: optionator
    version: 0.8.3
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: registry.npmmirror.com/deep-is/0.1.4
      fast-levenshtein: registry.npmmirror.com/fast-levenshtein/2.0.6
      levn: registry.npmmirror.com/levn/0.3.0
      prelude-ls: registry.npmmirror.com/prelude-ls/1.1.2
      type-check: registry.npmmirror.com/type-check/0.3.2
      word-wrap: registry.npmmirror.com/word-wrap/1.2.5
    dev: false

  registry.npmmirror.com/param-case/2.1.1:
    resolution: {integrity: sha512-eQE845L6ot89sk2N8liD8HAuH4ca6Vvr7VWAWwt7+kvvG5aBcPmmphQ68JsEG2qa9n1TykS2DLeMt363AAH8/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/param-case/-/param-case-2.1.1.tgz}
    name: param-case
    version: 2.1.1
    dependencies:
      no-case: registry.npmmirror.com/no-case/2.3.2
    dev: false

  registry.npmmirror.com/prelude-ls/1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.1.2.tgz}
    name: prelude-ls
    version: 1.1.2
    engines: {node: '>= 0.8.0'}
    dev: false

  registry.npmmirror.com/relateurl/0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/relateurl/-/relateurl-0.2.7.tgz}
    name: relateurl
    version: 0.2.7
    engines: {node: '>= 0.10'}
    dev: false

  registry.npmmirror.com/source-map/0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz}
    name: source-map
    version: 0.5.7
    engines: {node: '>=0.10.0'}
    dev: false

  registry.npmmirror.com/source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz}
    name: source-map
    version: 0.6.1
    engines: {node: '>=0.10.0'}
    dev: false

  registry.npmmirror.com/type-check/0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-check/-/type-check-0.3.2.tgz}
    name: type-check
    version: 0.3.2
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmmirror.com/prelude-ls/1.1.2
    dev: false

  registry.npmmirror.com/uglify-js/3.4.10:
    resolution: {integrity: sha512-Y2VsbPVs0FIshJztycsO2SfPk7/KAF/T72qzv9u5EpQ4kB2hQoHlhNQTsNyy6ul7lQtqJN/AoWeS23OzEiEFxw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uglify-js/-/uglify-js-3.4.10.tgz}
    name: uglify-js
    version: 3.4.10
    engines: {node: '>=0.8.0'}
    hasBin: true
    dependencies:
      commander: registry.npmmirror.com/commander/2.19.0
      source-map: registry.npmmirror.com/source-map/0.6.1
    dev: false

  registry.npmmirror.com/upper-case/1.1.3:
    resolution: {integrity: sha512-WRbjgmYzgXkCV7zNVpy5YgrHgbBv126rMALQQMrmzOVC4GM2waQ9x7xtm8VU+1yF2kWyPzI9zbZ48n4vSxwfSA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/upper-case/-/upper-case-1.1.3.tgz}
    name: upper-case
    version: 1.1.3
    dev: false

  registry.npmmirror.com/word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz}
    name: word-wrap
    version: 1.2.5
    engines: {node: '>=0.10.0'}
    dev: false
