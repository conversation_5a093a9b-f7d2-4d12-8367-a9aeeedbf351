{"name": "@mantas/cli", "version": "2.9.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "publish:major": "npm version major && npm publish", "publish:minor": "npm version minor && npm publish", "publish:patch": "npm version patch && npm publish"}, "bin": {"mantas": "./src/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@mantas/request": "^1.0.10", "art-template": "^4.13.2", "chalk": "^4.1.0", "commander": "^6.0.0", "dotenv": "^16.0.1", "inquirer": "^7.3.3", "miniprogram-ci": "^1.8.35", "ora": "^5.0.0"}, "devDependencies": {"husky": "^4.2.5"}}