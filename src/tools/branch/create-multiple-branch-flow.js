const childProcess = require('child_process');
const chalk = require('chalk');
const execSync = childProcess.execSync;
const resolveJson = require('../resolve-json.js');
const ora = require('ora');
const {
  getNewestBranchFlow,
  branchFlowPath,
  updateBranchToRemotePath,
} = require('../../path.js');
const findLargestVersion = require('../../utils/find-largest-version.js');

module.exports = (releaseInfoPath) => {
  let projectList = [];
  const spinner = ora('正在切branch，请稍等...\n').start();

  try {
    let branchName = '';
    const data = resolveJson(releaseInfoPath, { projectList: [] });
    projectList = data.projectList;

    if (!projectList || !projectList.length) {
      throw new Error(chalk.red('没有待发布项目记录'));
    }

    console.log(data.outBranch);
    if (data.outBranch) {
      branchName = data.outBranch;
    } else {
      // 获取到需要的分支号；如果已经提供则不需要
      const branchList = projectList
        .filter((project) => project.release)
        .map((project) => {
          // console.log(project);
          const result = execSync(
            `/bin/sh ${getNewestBranchFlow} ${project.path}`,
            { encoding: 'utf8' }
          );
          return result.replace('\n', '');
        });
      // console.log(branchList, findLargestVersion(branchList, "d"));
      branchName = findLargestVersion(branchList, 'd');
    }

    projectList
      .filter((project) => project.release)
      .forEach((project) => {
        let createNewBranchCmd = `/bin/sh ${branchFlowPath} -n ${branchName} -p ${project.path}`;
        const result = execSync(createNewBranchCmd, { encoding: 'utf8' });
        console.log(result);
        execSync(`/bin/sh ${updateBranchToRemotePath}`, { encoding: 'utf8' });
      });

    spinner.text = '成功';
    spinner.clear().succeed();
  } catch (err) {
    console.log(chalk.red(err));
    // console.log(chalk.red(`遇到错误：${err}`));
    spinner.text = '错误';
    spinner.clear().fail();
  }
};
