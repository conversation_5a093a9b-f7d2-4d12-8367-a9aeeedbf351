const execSync = require("child_process").execSync;
const chalk = require("chalk");
const ora = require("ora");

const { branchFlowPath, updateBranchToRemotePath } = require("../../path");

module.exports = (branchName) => {
  let createNewBranchCmd = `/bin/sh ${branchFlowPath}`;
  if (branchName) {
    createNewBranchCmd = `${createNewBranchCmd} -n ${branchName}`;
  }
  const spinner = ora("正在切branch，请稍等...\n").start();

  try {
    const result = execSync(`${createNewBranchCmd}`, { encoding: "utf-8" });
    console.log(chalk.blue(result));
    execSync(`/bin/sh ${updateBranchToRemotePath}`, { encoding: "utf-8" });

    spinner.text = "成功";
    spinner.clear().succeed();
  } catch (err) {
    console.log(chalk.red(err));
    // console.log(chalk.red(`遇到错误：${err}`));
    spinner.text = "错误";
    spinner.clear().fail();
  }
};
