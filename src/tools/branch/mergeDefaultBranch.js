const execSync = require('child_process').execSync;
const { mergeDefaultBranchPath, updateBranchToRemotePath } = require('../../path');
const chalk = require('chalk');

module.exports = () => {
    const updateBranchToRemoteCmd = `/bin/sh ${updateBranchToRemotePath}`;
    const mergeDefaultBranchCmd = `/bin/sh ${mergeDefaultBranchPath}`;
    const cmd = `${updateBranchToRemoteCmd} && ${mergeDefaultBranchCmd}`;
    try {
        const result = execSync(cmd);
        console.log(chalk.blue(result));
    } catch (e) {
        console.trace(e);
    }
};
