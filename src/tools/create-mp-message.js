const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const inquirer = require('inquirer');

module.exports = async () => {
    const currentPath = process.cwd();
    const projectJson = path.resolve(currentPath, 'project.config.json');
    const configProjectJson = path.resolve(currentPath, 'config', 'project.config.template.json');
    // console.log('message', fs.existsSync(projectJson), fs.existsSync(configProjectJson));
    // 非小程序项目，直接退出
    if (!fs.existsSync(projectJson) && !fs.existsSync(configProjectJson)) return ;
    
    console.log(chalk.blue(`检测到当前项目为小程序项目，请输入上传代码时的上传备注（如：当前上传的功能描述）。`));
    
    const questions = [{
        name: 'message',
        type: 'input',
        message: '请输入当前小程序项目提交时的备注信息：',
    }];
    const ans = await inquirer.prompt(questions);
    return ans.message;
};