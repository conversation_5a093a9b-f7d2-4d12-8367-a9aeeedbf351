const fs = require("fs");
const childProcess = require("child_process");
const chalk = require("chalk");
const execSync = childProcess.execSync;
const resolveJson = require("../resolve-json.js");
const {
  getReleaseTagBranch,
  createReleaseTagBranch,
  releaseBranchLogic,
  tagInfoFilePath,
} = require("../../path");

module.exports = (releaseInfoPath) => {
  let projectList = [];
  try {
    const data = resolveJson(releaseInfoPath, { projectList: [] });
    projectList = data.projectList;

    if (!projectList || !projectList.length) {
      throw new Error(chalk.red("没有待发布项目记录"));
    }

    // execSync(`/bin/sh ${getReleaseTagBranch}`);
    // const releaseTag = (fs.readFileSync(tagInfoFilePath, {encoding: 'utf8'})).trim();
    // fs.rmSync(tagInfoFilePath);

    projectList
      .filter((project) => project.release)
      .forEach((project) => {
        const result = execSync(
          `/bin/sh ${releaseBranchLogic} -p ${project.path} -o ${data.outBranch}`,
          { encoding: "utf8" }
        );
        console.log(result);
      });

    // // 根据recordList生成tag
    // recordList
    //   .filter(record => record.branch && record.tag)
    //   .forEach(record => {
    //     const project = projectList.find(item => item.name === record.name);
    //     if (project) {
    //         // 打tag
    //         const log = execSync(`/bin/sh ${createReleaseTagBranch} ${project.path} ${releaseTag} ${record.name}`, {encoding: 'utf8'});
    //         console.log(chalk.blue(log));
    //     }
    //   });
  } catch (e) {
    // rollUpReleaseTagBranchFlow(projectList, recordList);
    console.log(e);
  }
};
