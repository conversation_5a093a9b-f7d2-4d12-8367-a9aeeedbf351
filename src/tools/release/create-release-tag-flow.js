const childProcess = require("child_process");
const findLargestVersion = require("../../utils/find-largest-version");
const execSync = childProcess.execSync;
const resolveJson = require("../resolve-json");
const { getNewestTagFlow, createTag } = require("../../path");
const chalk = require("chalk");

module.exports = (jsonPath) => {
  try {
    // 解析json文件
    const jsonData = resolveJson(jsonPath, { projectList: [] });
    const projectList = jsonData.projectList.filter((item) => item.release);
    const outBranch = jsonData.outBranch;
    if (!projectList || !projectList.length) {
      throw new Error(chalk.red("没有待发布项目记录"));
    }
    // 1. 在每个项目中的tagList里面找出最大的那个tag号
    // 2. 将每个项目中最大的tag号放在一起
    const tagList = projectList.map((project) => {
      const getNewestTagFlowSh = `/bin/sh ${getNewestTagFlow} ${project.path} ${project.name}`;
      console.log(chalk.blue(`${project.name} 正在获取自身最新的tag`));
      const result = execSync(getNewestTagFlowSh, { encoding: "utf8" });
      return result.replace("\n", "");
    });
    // 3. 找出最大tagList中的最大的那个tag号
    const tag = findLargestVersion(tagList);
    console.log(chalk.blue(tag));
    projectList.forEach((project) => {
      const createTagSh = `/bin/sh ${createTag} ${project.path} ${project.name} ${tag} ${outBranch}`;
      const log = execSync(createTagSh, { encoding: "utf8" });
      console.log(chalk.blue(log));
    });
    const resultLogList = [
      "项目：",
      ...projectList.map((item) => item.name),
      `分支：${outBranch}`,
      `tag号：${tag}`,
    ];
    console.log(chalk.green(`${resultLogList.join("\n")}`));
  } catch (e) {
    console.log(e);
  }
};
