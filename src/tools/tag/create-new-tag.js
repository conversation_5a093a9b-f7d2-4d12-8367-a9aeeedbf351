
const exec = require('child_process').exec;
const execSync = require('child_process').execSync;
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs');

const { tagFlowPath, updateBranchToRemotePath } = require('../../path');

module.exports = (message) => {
    let updateBranchToRemoteCmd = `/bin/sh ${updateBranchToRemotePath}`;
    let createNewTagCmd = `/bin/sh ${tagFlowPath}`;
    const spinner = ora('正在打tag，请稍等...\n').start();

    try {
      if (message) {
        createNewTagCmd = `${createNewTagCmd} -m ${message}`;
      }
      const result = execSync(`${updateBranchToRemoteCmd} && ${createNewTagCmd}`, {encoding: 'utf-8'});
      console.log(chalk.blue(result));

      spinner.text = '成功';
      spinner.clear().succeed();
    } catch (err) {
      console.log(chalk.red(err));
      // console.log(chalk.red(`遇到错误：${err}`));
      spinner.text = '错误';
      spinner.clear().fail();
    }
  
    // exec(`${updateBranchToRemoteCmd} && ${createNewTagCmd}`, (err, stdout, stderr) => {
    //   if (err) {
    //     console.log(chalk.red(`遇到错误：${err}`));
    //     spinner.text = '错误';
    //     spinner.clear().fail();
    //     return ;
    //   }
  
    //   console.log(chalk.blue(stdout));
    //   spinner.text = '成功';
    //   spinner.clear().succeed();
    // });
  }