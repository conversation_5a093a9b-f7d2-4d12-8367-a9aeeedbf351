const fs = require('fs');
const childProcess = require('child_process');
const chalk = require('chalk');
const execSync = childProcess.execSync;
const rollUpReleaseTagBranchFlow = require('./roll-up-release-tag-branch-flow');
const { getReleaseTagBranch, createReleaseTagBranch, releaseBranchLogic, tagInfoFilePath } = require('../../path');

module.exports = (releaseInfoPath) => {
  let projectList = [];
  const recordList = [];
  try {
    const data = fs.readFileSync(releaseInfoPath, 'utf8');
    projectList = JSON.parse(data);

    if (!projectList || !projectList.length) {
      throw new Error(chalk.red('没有待发布项目记录'));
    }

    execSync(`/bin/sh ${getReleaseTagBranch}`);
    const releaseTag = (fs.readFileSync(tagInfoFilePath, {encoding: 'utf8'})).trim();
    fs.rmSync(tagInfoFilePath);
    console.log(releaseTag);

    projectList
      .filter(project => project.tagList && project.tagList.length)
      .forEach(project => {
        // 生成record记录
        recordList.push({
          name: project.name,
          branch: releaseTag,
          tag: releaseTag,
        });
        const result = execSync(`/bin/sh ${releaseBranchLogic} -p ${project.path} -o ${releaseTag}`, {encoding: 'utf8'});
        console.log(result);
        // 合并所有tag
        project.tagList.forEach(tag => {
          childProcess.execSync(`cd ${project.path} && pwd && git merge ${tag}`, {encoding: 'utf8'});
          console.log(`项目${project.name}, 合并${tag}`);
        });
      });

    // // 根据recordList生成tag
    recordList
      .filter(record => record.branch && record.tag)
      .forEach(record => {
        const project = projectList.find(item => item.name === record.name);
        if (project) {
            // 打tag
            const log = execSync(`/bin/sh ${createReleaseTagBranch} ${project.path} ${releaseTag} ${record.name}`, {encoding: 'utf8'});
            console.log(chalk.blue(log));
        }
      });

  } catch (e) {
    rollUpReleaseTagBranchFlow(projectList, recordList);
    console.log(e);
  }
};