const path = require('path');
// page/crud下的文件
const crudPath = path.resolve(__dirname, 'pages/crud');
const crudIndex = path.resolve(crudPath, 'index.art');
const crudInterface = path.resolve(crudPath, 'interface.art');
const crudUtil = path.resolve(crudPath, 'util.art');
const crudListIndex = path.resolve(crudPath, 'List/index.art');
const crudEditIndex = path.resolve(crudPath, 'Edit/index.art');
const crudIndexLess = path.resolve(crudPath, 'index.less.art');
// page/page下的文件
const pagePath = path.resolve(__dirname, 'pages/page');
const pageIndex = path.resolve(pagePath, 'page.art');
// model下的文件
const modelsPath = path.resolve(__dirname, 'models');
const model = path.resolve(modelsPath, 'model.art');
const modelFetch = path.resolve(modelsPath, 'fetch.art');
const modelInterface = path.resolve(modelsPath, 'interface.art');
const modelUtil = path.resolve(modelsPath, 'util.art');
const modelApi = path.resolve(modelsPath, 'api.art');

// 命名规范：name由"-"链接，前面为模板名，后面为创建后文件的后缀

module.exports = {
  pages: {
    crud: {
      index: {
        path: '.',
        name: 'index.tsx',
        src: crudIndex,
      },
      interface: {
        path: '.',
        name: 'interface.ts',
        src: crudInterface,
      },
      indexLess: {
        path: '.',
        name: 'index.less',
        src: crudIndexLess,
      },
      util: {
        path: '.',
        name: 'util.ts',
        src: crudUtil,
      },
      list: {
        path: 'List',
        name: 'index.tsx',
        src: crudListIndex,
      },
      edit: {
        path: 'Edit',
        name: 'index.tsx',
        src: crudEditIndex,
      },
    },
    page: {
      page: {
        path: '.',
        name: 'index.tsx',
        src: pageIndex,
      },
    },
  },
  models: {
    model: {
      path: '.',
      name: 'model.ts',
      src: model,
    },
    fetch: {
      path: '.',
      name: 'fetch.ts',
      src: modelFetch,
    },
    interface: {
      path: '.',
      name: 'interface.ts',
      src: modelInterface,
    },
    util: {
      path: '.',
      name: 'util.ts',
      src: modelUtil,
    },
    api: {
      path: '.',
      name: 'api.ts',
      src: modelApi,
    },
  },
};
