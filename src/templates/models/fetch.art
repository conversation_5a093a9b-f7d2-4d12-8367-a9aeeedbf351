import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  {{ModuleName}}ListParam,
  {{ModuleName}},
  {{ModuleName}}Param,
  {{ModuleName}}Detail,
} from './interface';
import { {{moduleName}}ApiOption } from './api';

// 获取{{ModuleName}}列表
export const fetch{{ModuleName}}List = (param?: {{ModuleName}}ListParam): Promise<Data<{{ModuleName}}>> => {
  const config = {{moduleName}}ApiOption.{{moduleName}}List;
  if (param) {
    config.option.params = param
  }
  return request(config.url, config.option);
};

// 创建{{ModuleName}}
export const fetch{{ModuleName}}Creation = (param: {{ModuleName}}Param): Promise<ApiSuccessEnum> => {
  const config = {{moduleName}}ApiOption.{{moduleName}}Creation;
  config.option.data = param
  return request(config.url, config.option);
};

// 更新{{ModuleName}}
export const fetch{{ModuleName}}Update = (param: {{ModuleName}}Param): Promise<ApiSuccessEnum> => {
  const config = {{moduleName}}ApiOption.{{moduleName}}Update;
  config.option.data = param
  return request(config.url, config.option);
};

// 根据id获取{{ModuleName}}
export const fetch{{ModuleName}}Detail = (id: number): Promise<{{ModuleName}}Detail> => {
  const config = {{moduleName}}ApiOption.{{moduleName}}Detail;
  config.option.params = {id};
  return request(config.url, config.option);
};

// 删除{{ModuleName}}
export const fetch{{ModuleName}}Deletion = (id: number): Promise<ApiSuccessEnum> => {
  const config = {{moduleName}}ApiOption.{{moduleName}}Deletion;
  config.option.params = {id};
  return request<never>(config.url, config.option);
};