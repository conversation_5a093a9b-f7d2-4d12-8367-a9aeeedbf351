import { Effect, Reducer } from '@umijs/max';
import { {{ModuleName}} } from './interface';
import {
    fetch{{ModuleName}}List,
} from './fetch';

export interface {{ModuleName}}State {
}

export const init{{ModuleName}}State: {{ModuleName}}State = {
};

export interface {{ModuleName}}Model {
  namespace: '{{moduleName}}';
  state: {{ModuleName}}State;
  effects: {
    request{{ModuleName}}List: Effect;
  };
  reducers: {
    request{{ModuleName}}ListSuccess: Reducer<
      {{ModuleName}}State,
      { type: 'request{{ModuleName}}ListSuccess'; payload: {{ModuleName}}[] }
    >;
  };
}

const {{moduleName}}Model: {{ModuleName}}Model = {
  namespace: '{{moduleName}}',
  state: init{{ModuleName}}State,
  effects: {
    *request{{ModuleName}}List(action, { call, put }) {
      const {{moduleName}}List: {{ModuleName}}[] = yield call(fetch{{ModuleName}}List);
      yield put({
        type: 'request{{ModuleName}}ListSuccess',
        payload: {{moduleName}}List,
      });
    },
  },
  reducers: {
    request{{ModuleName}}ListSuccess(
      state = init{{ModuleName}}State,
      { payload },
    ): {{ModuleName}}State {
      return {
        ...state,
        {{moduleName}}List: payload,
      };
    },
  },
};

export default {{moduleName}}Model;