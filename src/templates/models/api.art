import { RequestMethod, RequestOption } from '@mantas/request';

export const {{moduleName}}ApiOption: Record<
  | '{{moduleName}}List'
  | '{{moduleName}}Creation'
  | '{{moduleName}}Update'
  | '{{moduleName}}Detail'
  | '{{moduleName}}Deletion',
  RequestOption
> = {
  // 获取{{ModuleName}}列表
  {{moduleName}}List: {
    url: '/{{moduleName}}/list',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 创建{{ModuleName}}
  {{moduleName}}Creation: {
    url: '/{{moduleName}}',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 更新{{ModuleName}}
  {{moduleName}}Update: {
    url: '/{{moduleName}}',
    option: {
      method: RequestMethod.Put,
    },
  },
  // 根据{{ModuleName}}的id获取详情数据
  {{moduleName}}Detail: {
    url: '/{{moduleName}}',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 删除{{ModuleName}}
  {{moduleName}}Deletion: {
    url: '/{{moduleName}}',
    option: {
      method: RequestMethod.Delete,
    },
  },
};