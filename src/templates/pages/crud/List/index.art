import { Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { history } from '@umijs/max';
import { PlusOutlined } from '@ant-design/icons';
import { BaseQueryFilterProps, ProTable,  ProColumns } from '@ant-design/pro-components';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { {{ModuleName}}, {{ModuleName}}ListParam } from '@/models/{{moduleName}}/interface';
import { init{{ModuleName}}ListParam } from '@/models/{{moduleName}}/util';
import { fetch{{ModuleName}}List } from '@/models/{{moduleName}}/fetch';
import { Paginator, initPaginator } from '@/utils/request';
import { {{ModuleName}}Form } from '../interface';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<{{ModuleName}}[]>([]);
  const [listParam, setListParam] = useState<{{ModuleName}}ListParam>(init{{ModuleName}}ListParam);
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);

  // 获取列表数据
  const request{{ModuleName}}List = async (param: {{ModuleName}}ListParam = init{{ModuleName}}ListParam) => {
    const { items, ...rest } = await fetch{{ModuleName}}List(param);
    setPaginator(rest);
    setDataList(items);
  };

  // 编辑
  const edit{{ModuleName}} = (id: number) => {
    history.push(`/edit/${id}`);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: {{ModuleName}}Form = form?.getFieldsValue();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: ``,
              param: { ...formData },
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: ``,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<{{ModuleName}}>> = [];

  useEffect(() => {
    request{{ModuleName}}List(listParam);
  }, [listParam]);

  return (
    <ProTable<{{ModuleName}}>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search=<%-'{{'%>
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      <%-'}}'%>
      options=<%-'{{'%>
        reload: () => {
          if (listParam) setListParam({ ...listParam });
        },
      <%-'}}'%>
      scroll=<%-'{{'%>
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      <%-'}}'%>
      toolbar=<%-'{{'%>
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => edit{{ModuleName}}(0)}
          >
            新增
          </Button>,
        ],
      <%-'}}'%>
      pagination=<%-'{{'%>
        pageSize: paginator.limit,
        total: paginator.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      <%-'}}'%>
    />
  );
};

export default List;