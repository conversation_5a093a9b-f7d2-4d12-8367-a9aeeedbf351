import {
  {{ModuleName}}Detail,
  {{ModuleName}}Param,
} from '@/models/{{moduleName}}/interface';
import { {{ModuleName}}Form } from './interface';

export const initial{{ModuleName}}Form: {{ModuleName}}Form = {
};

// 将formData转换为param
export const transferFormDataToParam = (formData: {{ModuleName}}Form, id?: number): {{ModuleName}}Param => {
  const param: {{ModuleName}}Param = {
  };
  if (id) {
    param.id = id;
  }
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: {{ModuleName}}Detail): {{ModuleName}}Form => {
  const formData: {{ModuleName}}Form = {
  };
  return formData;
};