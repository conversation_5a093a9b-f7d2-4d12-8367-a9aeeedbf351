#!/usr/bin/env node

const commander = require('commander');
const inquirer = require('inquirer');
const package = require('../package.json');
const exec = require('child_process').exec;
const chalk = require('chalk');
const ora = require('ora');

const newOperation = require('./operation/new');
const tagOperation = require('./operation/tagOperate');

const { betaTagPath, deleteTagPath, showCurrentPath } = require('./path');
const getFiles = require('./operation/getFiles');
const template = require('./operation/template');
const miniprogramCi = require('./miniprogram-ci');
const submitTestOperation = require('./operation/submitTest');

commander.version(package.version);

// git操作
commander
  .command('get beta tag')
  .description('获取最新的线上tag')
  .action(() => {
    operateNewestBetaTag('get');
  });

commander
  .command('merge beta tag')
  .description('合并线上最新的线上tag')
  .action(() => {
    // operateNewestBetaTag('merge');
    tagOperation.merge();
  });

commander
  .command('checkout beta tag')
  .description('切换到最新的线上tag版本')
  .action(() => {
    operateNewestBetaTag('checkout');
  });

commander
  .command('create tag')
  .description('创建一个新的tag（非线上tag）')
  .action(async () => {
    const message = await newOperation.normal.message();
    newOperation.normal.tag({ message });
  });

commander
  .command('delete tag')
  .description('删除指定规则的本地与远程的tag')
  .option('-p, --partern <partern>', '请输入tag的规则')
  .option('-P, --processor <processor>', '请输入tag的规则')
  .action((options) => {
    // console.log(options.partern);
    deleteTag(options.partern, options.processor);
  });

/**
 * 创建branch
 * @type {string}
 * @since v2.5.0
 */
// commander
//   .command("create branch")
//   .description("创建一个新的branch")
//   .option("-n, --name <name>", "请输入branch的名称")
//   .action(async (options) => {
//     console.log(options);
//     // const message = await newOperation.normal.message();
//     // newOperation.normal.tag(message);
//   });

// sql操作
commander
  .command('insert sql')
  .description('创建一条菜单权限SQL语句')
  .action(() => {
    insertSql();
  });

/**
 * ========================================
 * 发布相关
 */
// 分支操作
commander
  .command('new <character> <type>')
  .description('创建发布用的分支 or tag')
  .option('-c --config <config>', '请指定配置文件')
  .option('-m --message <message>', '请输入tag的信息')
  .option(
    '-b --branch <branch>',
    '请指定要切的分支号，将会从origin/master上切出'
  )
  .action((character, type, options) => {
    let param = {};
    if (options.config) {
      param.jsonPath = options.config;
    }
    if (options.branch) {
      param.branch = options.branch;
    }
    newOperation[character][type](param);
  });
/**
 * ========================================
 */

/**
 * ========================================
 * f2elint相关
 */
// 查询需要操作的文件列表
commander
  .command('ls-git-file')
  .description('找出git仓库文件')
  // 已跟踪内容
  .option('-t --tracked', '指定为已跟踪的文件')
  // 未跟踪内容
  .option('-u --untracked', '指定为未跟踪的文件')
  // 所有未提交文件
  .option('-U --uncommitted', '指定为未提交的文件')
  // 找出与default分支存在差异的文件列表
  .option('-d --diff <diff>', '指定是否要找出与default分支存在差异的文件列表')
  .action((options) => {
    const files = getFiles(options);
    console.log(files);
  });

// 代码风格检查
commander
  .command('check-code-style')
  .description('检查代码风格')
  // 已跟踪内容
  .option('-t --tracked', '指定为已跟踪的文件内容')
  // 未跟踪内容
  .option('-u --untracked', '指定为未跟踪的文件内容')
  // 所有未提交文件
  .option('-U --uncommitted', '指定为未提交的文件内容')
  // 找出与default分支存在差异的文件列表
  .option('-d --diff <diff>', '指定是否要检查与default分支存在差异的文件内容')
  .action((options) => {
    const files = getFiles(options);
    console.log('不再支持了！');
  });

/**
 * ========================================
 */

/**
 * ========================================
 * 模板代码生成
 */
commander
  .command('generate <moduleName>')
  .description('生成代码')
  .alias('g')
  // 生成普通的page
  .option('-n --normal', '生成普通的代码文件')
  // 生成普通的CRUD的page
  .option('-c --crud', '生成普通的CRUD的page')
  // 生成普通的model，包含api-module信息
  .option('-m --model', '生成普通的model，包含api-module信息')
  // 找出与default分支存在差异的文件列表
  .action(template.run);

/**
 * ========================================
 */

/**
 * ========================================
 * 小程序自动上传
 */
commander
  .command('mini-ci')
  .description('小程序上传，预览等miniprogram-ci功能封装')
  .alias('m')
  // 目前只支持上传功能支持
  .option('-u --upload', '上传小程序代码')
  .option('-un --username <username>', '上传小程序代码的操作人')
  .option('-t --tag <tag>', '目标小程序的版本号信息')
  .option('-e --env <env>', '目标小程序的环境')
  .option('-p --path <path>', '目标小程序的环境配置路径')
  // 找出与default分支存在差异的文件列表
  .action((options) => {
    miniprogramCi.upload({
      tag: options.tag,
      envType: options.env,
      username: options.username,
      isSaas: options.Saas,
      path: options.path,
    });
  });

/**
 * ========================================
 */

/**
 * ========================================
 * submitTest 相关
 */
commander
  .command('submitTest')
  .description('提交测试相关操作')
  .alias('st')
  .option('-m, --main <main>', '主分支名称', 'master')
  .option('-t, --test <test>', '主测试分支名称')
  .option('-b, --branch <branch>', '当前分支名称')
  .action((options) => {
    // 获取当前分支名作为默认值
    if (!options.branch) {
      const getCurrentBranchCmd = 'git branch --show-current';
      const { execSync } = require('child_process');
      try {
        options.branch = execSync(getCurrentBranchCmd, {
          encoding: 'utf8',
        }).trim();
      } catch (error) {
        console.log(chalk.red('无法获取当前分支名'));
        options.branch = 'unknown';
      }
    }

    submitTestOperation.submitTest({
      main: options.main,
      test: options.test,
      branch: options.branch,
    });
  });

/**
 * ========================================
 */

commander.parse(process.argv);

function operateNewestBetaTag(operator) {
  // let updateBranchToRemoteCmd = `/bin/sh ${updateBranchToRemotePath}`;
  let getNewestBetaTagCmd = `/bin/sh ${betaTagPath}`;
  let loadingTip = '正在获取最新的beta tag\n';

  if (operator !== 'get') {
    // getNewestBetaTagCmd = `${getNewestBetaTagCmd} | xargs git ${operator}`;
    getNewestBetaTagCmd = `git ${operator} origin/master`;
    loadingTip = `正在${operator}最新的beta tag\n`;
  }

  // if (operator === 'merge') {
  //   getNewestBetaTagCmd = `${updateBranchToRemoteCmd} && git pull && ${getNewestBetaTagCmd} && git push`;
  // }

  console.log(getNewestBetaTagCmd);

  const spinner = ora(loadingTip).start();
  exec(getNewestBetaTagCmd, (err, stdout, stderr) => {
    if (err) {
      console.log(chalk.red(`遇到错误：${err}`));
      spinner.text = '错误';
      spinner.clear().fail();
      return;
    }

    console.log(chalk.blue(stdout));
    spinner.text = '成功';
    spinner.clear().succeed();
  });
}

function insertSql() {
  const moduleList = ['chain', 'admin', 'supplier', 'b2b', 'crm'];
  let sql = '';
  const questions = [
    {
      name: 'permission',
      type: 'input',
      message: '请输入要添加的权限：',
    },
    {
      name: 'title',
      type: 'input',
      message: '请输入添加的菜单名称：',
    },
    {
      name: 'module',
      type: 'list',
      message: '请输入所属平台：',
      default: 'chain',
      choices: moduleList.map((module) => ({
        name: module,
        value: module,
      })),
    },
    {
      name: 'url',
      type: 'input',
      message: '请输入URL：',
    },
    {
      name: 'sort',
      type: 'number',
      message: '请输入排序（仅限数字）：',
    },
    {
      name: 'titlePermission',
      type: 'input',
      message: '父级权限值：',
    },
    {
      name: 'icon',
      type: 'input',
      message: 'icon地址：',
    },
  ];
  inquirer.prompt(questions).then((ans) => {
    // const spinner = ora('SQL生成中，请稍等...\n').start();
    try {
      let sql = '';
      const { permission, title, module, url, sort, titlePermission, icon } =
        ans;
      if (titlePermission) {
        sql = `insert into permission (permission, name, description, module, parent_id, url, sort, is_delete, icon) select '${permission}', '${title}', '${title}', '${module}', id, '${url}', ${sort}, 0, '${icon}' from \`permission\` where permission='${titlePermission}';`;
      } else {
        sql = `insert into permission (permission, name, description, module, parent_id, url, sort, is_delete, icon) VALUES ('${permission}', '${title}', '${title}', '${module}', NULL, '${url}', ${sort}, 0, '${icon}');`;
      }
      console.log('请复制下面的SQL：', sql);
      // spinner.text = '成功';
      // spinner.clear().succeed();
    } catch (err) {
      console.log(chalk.red(`遇到错误：${err}`));
      // spinner.text = e.name;
      // spinner.clear().fail();
    }
  });
}

function deleteTag(partern, processorNumber) {
  let deleteTagCmd = `/bin/sh ${deleteTagPath} ${partern} ${
    processorNumber || 3
  }`;

  const spinner = ora('正在打tag，请稍等...\n').start();
  exec(deleteTagCmd, (err, stdout, stderr) => {
    if (err) {
      console.log(chalk.red(`遇到错误：${err}`));
      spinner.text = '错误';
      spinner.clear().fail();
      return;
    }

    console.log(chalk.blue(stdout));
    spinner.text = '成功';
    spinner.clear().succeed();
  });
}

function showCurPath() {
  let showCurPathCmd = `/bin/sh ${showCurrentPath}`;

  const spinner = ora('正在打tag，请稍等...\n').start();
  exec(`${showCurPathCmd}`, (err, stdout, stderr) => {
    if (err) {
      console.log(chalk.red(`遇到错误：${err}`));
      spinner.text = '错误';
      spinner.clear().fail();
      return;
    }

    console.log(chalk.blue(stdout));
    spinner.text = '成功';
    spinner.clear().succeed();
  });
}
