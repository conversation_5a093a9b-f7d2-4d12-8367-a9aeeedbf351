module.exports = (tagList, prefix) => {
  const _prefix = prefix || "t";
  const _tagList = tagList
    .filter((tag) => tag.indexOf("beta") === -1)
    .map((tag) => tag.replace(_prefix, ""))
    .sort((a, b) => {
      let k = 0;
      const item1 = a.split(".");
      const item2 = b.split(".");
      for (let i in item1) {
        let a1 = item1[i];
        let b1 = item2[i];
        if (typeof b1 === undefined) {
          k = -1;
          break;
        } else {
          if (a1 === b1) {
            continue;
          }
          k = Number(b1) - Number(a1);
          break;
        }
      }
      return k;
    });
  return `${_prefix}${_tagList[0]}`;
};
