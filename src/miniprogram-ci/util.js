const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');
const ci = require('miniprogram-ci');

function generateCiProject(env) {
  const ciProject = createCiProject(env);
  return ciProject;
}

// 获取env环境配置文件
function getFile(envPath) {
  const envFileBuffer = fs.readFileSync(path.resolve(__dirname, envPath));
  return dotenv.parse(envFileBuffer);
}

// 根据tag号生成提交信息
function generateSubmitMessageInfo(tag, username) {
  const date = new Date();
  let message = username + ' 提交上传: ';
  // message += date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日' + date.getHours() + '点' + date.getMinutes() + '分 提交上传';
  let content = '';
  if (fs.existsSync(`${process.cwd()}/log/${tag}`)) {
    content = fs.readFileSync(`${process.cwd()}/log/${tag}`, {
      encoding: 'utf-8',
    });
  }
  if (content) {
    message = message + '：' + content;
  }
  return { tag, message };
}

function createCiProject(env) {
  // 注意： new ci.Project 调用时，请确保项目代码已经是完整的，避免编译过程出现找不到文件的报错。
  const project = new ci.Project({
    type: 'miniProgram',
    ignores: ['node_modules/**/*'],
    appid: env.APP_ID,
    projectPath: env.PROJECT_PATH,
    privateKeyPath: env.PRIVATE_KEY_PATH,
    // privateKeyPath: env.PRIVATE_KEY_PATH.replace('/root', '/Users/<USER>'),
    // appid: 'wxa8116ce1a098af54',
    // projectPath: 'dist/chain-medical-sandbox',
    // privateKeyPath: '../tools/ssh-key/private.wxa8116ce1a098af54.key',
  });
  return project;
}

function uploadMiniProgram(ciProject, env, messageInfo) {
  ci.upload({
    project: ciProject,
    version: messageInfo.tag,
    desc: messageInfo.message,
    setting: {
      urlCheck: false,
      es6: false,
      postcss: true,
      minified: false,
      newFeature: true,
    },
    useCOS: true,
    robot: +env.ROBOT_NO,
    threads: 5,
  }).catch((err) => {
    console.log(env, messageInfo, err);
    process.exit(-1);
  });
}

module.exports = {
  generateCiProject,
  getFile,
  generateSubmitMessageInfo,
  uploadMiniProgram,
};
