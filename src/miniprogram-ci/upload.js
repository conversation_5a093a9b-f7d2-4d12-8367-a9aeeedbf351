const {generateCiProject, getFile, generateSubmitMessageInfo, uploadMiniProgram, serializeArguments} = require('./util');

module.exports = ({tag, envType, username, isSaas, path}) => {  
  const submitMessageInfo = generateSubmitMessageInfo(tag, username);
  const envPath = path || '/tools/envs';
  const envFolder = process.cwd() + envPath;
  const userEnvPath = envFolder + '/' + envType + '.env';
  
  const env = getFile(userEnvPath);
  const ciProject = generateCiProject(env);
  
  uploadMiniProgram(ciProject, env, submitMessageInfo);
};


