const path = require('path');

const mantasCliPath = path.resolve(`${require.main.filename}/../../`);

const betaTagPath = path.join(mantasCliPath, 'bin/get-newest-beta-tag.sh');

const tagFlowPath = path.join(mantasCliPath, 'bin/start-tag-flow.sh');

const branchFlowPath = path.join(mantasCliPath, 'bin/start-branch-flow.sh');

const insertSqlPath = path.join(mantasCliPath, 'bin/insert-permission-sql.sh');

const deleteTagPath = path.join(mantasCliPath, 'bin/delete-tag.sh');

const showCurrentPath = path.join(mantasCliPath, 'bin/show-current-path.sh');

// release tag 操作
const tagFolderPath = path.join(mantasCliPath, 'bin/tag');
const releaseFolderPath = path.join(mantasCliPath, 'bin/release');
const branchFolderPath = path.join(mantasCliPath, 'bin/branch');
const fileFolderPath = path.join(mantasCliPath, 'bin/file');

// 提测操作
const submitTestPath = path.join(mantasCliPath, 'bin/submit-test.sh');

const tagInfoFilePath = path.join(mantasCliPath, 'tag-info');
const createReleaseTagBranch = path.join(
  tagFolderPath,
  'create-release-tag-branch.sh'
);
const getReleaseTagBranch = path.join(
  tagFolderPath,
  'get-release-tag-branch.sh'
);
const rollUpReleaseTagBranch = path.join(
  tagFolderPath,
  'roll-up-release-tag-branch.sh'
);
const getNewestTagFlow = path.join(tagFolderPath, 'get-newest-tag-flow.sh');
const createTag = path.join(tagFolderPath, 'create-tag.sh');
const releaseBranchLogic = path.join(
  releaseFolderPath,
  'release-branch-logic.sh'
);

// 合并branch操作
const mergeDefaultBranchPath = path.join(
  branchFolderPath,
  'merge-default-branch.sh'
);
const updateBranchToRemotePath = path.join(
  branchFolderPath,
  'update-branch-to-remote.sh'
);
const getDefaultBranchPath = path.join(
  branchFolderPath,
  'get-default-branch.sh'
);
const getNewestBranchFlow = path.join(
  branchFolderPath,
  'get-newest-branch-flow.sh'
);

// git file文件操作
const findTagBranchDiffFilesPath = path.join(
  fileFolderPath,
  'find-tag-branch-diff-files.sh'
);
const findUncommittedFilesPath = path.join(
  fileFolderPath,
  'find-uncommitted-files.sh'
);

module.exports = {
  betaTagPath,
  tagFlowPath,
  branchFlowPath,
  insertSqlPath,
  deleteTagPath,
  showCurrentPath,

  tagFolderPath,
  createReleaseTagBranch,
  releaseBranchLogic,
  tagInfoFilePath,
  getReleaseTagBranch,
  rollUpReleaseTagBranch,
  getNewestTagFlow,
  createTag,

  // branch
  mergeDefaultBranchPath,
  updateBranchToRemotePath,
  getDefaultBranchPath,
  // 获取最新的branch
  getNewestBranchFlow,

  // git-file
  findTagBranchDiffFilesPath,
  findUncommittedFilesPath,

  // 提测操作
  submitTestPath,
};
