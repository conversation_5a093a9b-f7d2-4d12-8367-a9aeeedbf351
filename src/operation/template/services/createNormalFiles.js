const inquirer = require('inquirer');
const createNormalModelFile = require('./createNormalModelFile');
const createNormalPageFile = require('./createNormalPageFile');

module.exports = async (moduleInfos) => {
    const fileTypes = ['model', 'page'];
    const {type} = await inquirer.prompt([{
        name: 'type',
        message: '请选择文件类型',
        type: 'list',
        choices: fileTypes.map(ft => ({
            name: ft,
            value: ft,
        })),
    }]);
    switch (type) {
        case 'model':
            createNormalModelFile(moduleInfos);
            break;
        default:
            createNormalPageFile(moduleInfos);
            break;
    }
};