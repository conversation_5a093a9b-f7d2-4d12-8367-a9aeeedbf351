const createFolder = require('../utils/createFolder');
const createFile = require('../utils/createFile');
const templates = require('../../../templates');
const createCurrentFolders = require('../utils/createCurrentFolders');

module.exports = (moduleInfos) => {
  if (!moduleInfos || !moduleInfos.length) return;

  try {
    // 新建一个pages文件夹
    const currentPath = createFolder('src/pages');

    createCurrentFolders(
      { rootPath: currentPath, moduleInfos, folderUpperCase: true },
      (modulePath, moduleInfos) => {
        // 生成CRUD的模板文件和代码
        for (let key in templates.pages.crud) {
          const value = templates.pages.crud[key];
          //   console.log(modulePath, value, moduleInfos);
          createFile(modulePath, value, moduleInfos);
        }
      },
      (modulePath, moduleInfos) => {
        createFile(modulePath, templates.pages.crud.index, moduleInfos);
      }
    );
  } catch (e) {
    console.error(e);
  }
};
