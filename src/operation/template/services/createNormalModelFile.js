const templates = require("../../../templates");
const createFile = require("../utils/createFile");
const createFolder = require("../utils/createFolder");
const createCurrentFolders = require("../utils/createCurrentFolders");

module.exports = (moduleInfos) => {
    let currentPath = createFolder('src/models');

    createCurrentFolders({rootPath: currentPath, moduleInfos}, (modulePath, {moduleName, ModuleName}) => {
        // 生成normal模板文件和代码
        createFile(modulePath, templates.models.model, {moduleName, ModuleName});
    });
};