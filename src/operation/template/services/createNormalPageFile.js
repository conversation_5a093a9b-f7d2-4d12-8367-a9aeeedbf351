const mkdir = require("../utils/mkdir");
const path = require('path');
const templates = require("../../../templates/index");
const createFile = require("../utils/createFile");
const createFolder = require("../utils/createFolder");
const createCurrentFolders = require("../utils/createCurrentFolders");

module.exports = (moduleInfos) => {
    const currentPath = createFolder('src/pages');

    createCurrentFolders({rootPath: currentPath, moduleInfos, folderUpperCase: true}, (modulePath, {moduleName, ModuleName}) => {
        // 生成normal模板文件和代码
        templates.pages.page.page.name = `${ModuleName}.tsx`
        createFile(modulePath, templates.pages.page.page, {moduleName, ModuleName});
    });
};