const templates = require('../../../templates/index');
const createFolder = require('../utils/createFolder');
const mkdir = require('../utils/mkdir');
const path = require('path');
const createFile = require('../utils/createFile');
const createCurrentFolders = require('../utils/createCurrentFolders');

module.exports = (moduleInfos) => {
  const modelsPath = createFolder('src/models');
  const currentPath = modelsPath;

  /**
   * 此处生成model & services/api下的文件和代码
   *
   * 由于model可以进行嵌套生成，而services却不能嵌套
   *
   * 所以，services/api下的目录只由moduleInfos中最后一个名称为api下的名称进行目录和文件的生成
   */

  createCurrentFolders(
    { rootPath: currentPath, moduleInfos },
    (modulePath, { moduleName, ModuleName }) => {
      // 生成services/api下的文件和代码
      // createFile(path.resolve(servicesApiPath, moduleName), templates.services.api.index, {moduleName, ModuleName});
      // 生成Models的模板文件和代码
      for (let key in templates.models) {
        if (key === 'model') continue;
        const value = templates.models[key];
        createFile(modulePath, value, { moduleName, ModuleName });
      }
    }
  );

  // moduleInfos.forEach((info, i) => {
  //     const _infoPath = path.resolve(modelsPath, moduleInfos[i - 1] ? moduleInfos[i - 1].ModuleName : '', info.ModuleName);
  //     let modulePath = _infoPath;
  //     let moduleName = info.moduleName;
  //     let ModuleName = info.ModuleName;
  //     mkdir(modulePath);

  //     if (i === moduleInfos.length - 1) {

  //     }
  // });
};
