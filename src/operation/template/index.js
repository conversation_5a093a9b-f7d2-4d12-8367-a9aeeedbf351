const createCRUDPages = require('./services/createCRUDPages');
const createModelPages = require('./services/createModelPages');
const createNormalFiles = require('./services/createNormalFiles');
const resolvePathAndNameInfo = require('./utils/resolvePathAndNameInfo');

module.exports = {
  run: (moduleName, { normal, crud, model }) => {
    moduleInfos = resolvePathAndNameInfo(moduleName);

    if (crud && model) {
      createCRUDPages(moduleInfos);
      createModelPages(moduleInfos);
      return;
    }

    // 处理普通文件
    if (normal) {
      createNormalFiles(moduleInfos);
      return;
    }

    // 处理crud的page
    if (crud) {
      createCRUDPages(moduleInfos);
      return;
    }

    // 处理model & api-module
    if (model) {
      createModelPages(moduleInfos);
      return;
    }
  },
};
