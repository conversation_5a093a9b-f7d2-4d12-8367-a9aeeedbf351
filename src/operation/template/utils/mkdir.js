const { log } = require('console');
const fs = require('fs');
const path = require('path');

module.exports = async (folderPath) => {

    const parentPath = path.dirname(folderPath);
    try {
        // 判断文件夹是否存在
        fs.accessSync(folderPath, fs.constants.F_OK);
        log(`${folderPath}文件夹已存在`);
    } catch (e) {
        log(`${folderPath}文件夹不存在，开始创建`);
        await fs.mkdirSync(folderPath);
        log(`${folderPath}创建成功`);
    }
};