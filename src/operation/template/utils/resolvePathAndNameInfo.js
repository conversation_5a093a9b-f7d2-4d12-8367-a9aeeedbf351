module.exports = (moduleName) => {
    const _paths = moduleName.split('/');
    const paths = _paths.map(_path => {
        const __path = _path.split('-').map(item => item[0].toUpperCase() + item.slice(1)).join('');
        return {
            // 模块名称
            moduleName: __path[0].toLowerCase() + __path.slice(1),
            ModuleName: __path[0].toUpperCase() + __path.slice(1),
            // 用于生成文件名的相关字段
            moduleNamePath: _path[0].toLowerCase() + _path.slice(1),
            ModuleNamePath:_path[0].toUpperCase() + _path.slice(1),
            // 模块路径
            modulePath: moduleName,
            ModulePath: _paths.map(item => item[0].toUpperCase() + item.slice(1)).join('/'),
        };
    });
    console.log(paths);
    return paths;
};
