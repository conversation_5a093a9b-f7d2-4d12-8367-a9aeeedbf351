const { log } = require('console');
const fs = require('fs');
const path = require('path');
const getFileName = require('./getFileName');
const mkdir = require('./mkdir');
const template = require('art-template');

module.exports = (folderPath, templateInfo, moduleInfo) => {
  const fileName = getFileName(templateInfo.name, moduleInfo.ModuleName);
  const html = template(templateInfo.src, moduleInfo);
  const filePath = path.resolve(folderPath, templateInfo.path, fileName);
  mkdir(path.dirname(filePath));
  try {
    // 判断文件是否存在
    fs.accessSync(filePath, fs.constants.F_OK);
    log(`${filePath}文件已存在`);
  } catch (e) {
    log(`${filePath}文件不存在，开始创建`);
    fs.writeFileSync(filePath, html, 'utf-8');
    log(`${filePath}创建成功`);
  }
};
