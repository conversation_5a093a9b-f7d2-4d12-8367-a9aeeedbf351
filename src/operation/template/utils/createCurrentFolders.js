const mkdir = require('./mkdir');
const path = require('path');

module.exports = (
  { moduleInfos, rootPath, folderUpperCase },
  fileCb,
  folderCb
) => {
  let currentPath = rootPath;

  moduleInfos.forEach((info, i) => {
    const infoModuleNamePath = folderUpperCase
      ? info.ModuleNamePath
      : info.moduleNamePath;
    currentPath = path.resolve(currentPath, infoModuleNamePath);
    let modulePath = currentPath;
    // let moduleName = info.moduleName;
    // let ModuleName = info.ModuleName;
    mkdir(modulePath);

    if (i === moduleInfos.length - 1) {
      typeof fileCb === 'function' && fileCb(modulePath, info);
    } else {
      typeof folderCb === 'function' && folderCb(modulePath, info);
    }
  });
};
