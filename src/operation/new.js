const createReleaseTagFlow = require("../tools/release/create-release-tag-flow");
const createNewTag = require("../tools/tag/create-new-tag");
const createReleaseBranchFlow = require("../tools/release/create-release-branch-flow");
const createMpMessage = require("../tools/create-mp-message");
const createNewBranch = require("../tools/branch/create-new-branch");
const createMultipleBranchFlow = require("../tools/branch/create-multiple-branch-flow");

module.exports = {
  release: {
    branch: (param) => {
      // 1. 获取需要本次需要进行发布的项目
      // 2. 针对每个项目，都操作一次：
      //   1). cd进入项目目录
      //   2). git checkout master
      //   3). mantas merge beta tag
      //   4). git checkout -b xxxx
      createReleaseBranchFlow(param.jsonPath);
    },

    tag: (param) => {
      // 1. 在每个项目中的tagList里面找出最大的那个tag号
      // 2. 将每个项目中最大的tag号放在一起
      // 3. 找出最大tagList中的最大的那个tag号
      createReleaseTagFlow(param.jsonPath);
    },
  },
  normal: {
    message: async () => {
      const message = createMpMessage();
      return message;
    },
    tag: (param) => {
      createNewTag(param.message);
    },
    branch: (param) => {
      if (param.jsonPath) {
        createMultipleBranchFlow(param.jsonPath);
      } else {
        createNewBranch(param.branchName || "");
      }
    },
  },
};
