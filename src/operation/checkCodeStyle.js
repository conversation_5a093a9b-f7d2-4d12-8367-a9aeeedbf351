const f2elint = require('f2elint');
const cwd = process.cwd();
// const fs = require('fs');
const failureReturn = function () {
  process.exit(1);
};

module.exports = (fileList) => {
    try {
    //   const result = fs.readFileSync(cwd + '/bin/files', { encoding: 'utf-8' });
    //   fs.rmSync(cwd + '/bin/files');
    //   const fileList = result.trim().split('\n');
    
        console.log('涉及文件如下：', fileList);
        
        f2elint
            .scan({
            cwd,
            files: fileList,
            // 只显示错误信息
            quiet: true,
            })
            .then(
            (res) => {
                if (res.errorCount) {
                console.error(new Error('有' + res.errorCount + '条代码风格错误，请修改后重新提交！'));
                failureReturn();
                }
            },
            (err) => {
                console.log('f2elint scan error', err);
                failureReturn();
            },
            );
    } catch (err) {
        console.log('try..catch error', err);
        failureReturn();
    }
};

