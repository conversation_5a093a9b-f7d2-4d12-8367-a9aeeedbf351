const { findUncommittedFilesPath, findTagBranchDiffFilesPath } = require('../path');

const execSync = require('child_process').execSync;

module.exports = {
    untracked: () => {
        const command = `/bin/sh ${findUncommittedFilesPath} -u`;
        const files = execSync(command, {encoding: 'utf8'});
        return files.replace(/\s/g, '\n').trim();
    },

    tracked: () => {
        const command = `/bin/sh ${findUncommittedFilesPath} -t`;
        const files = execSync(command, {encoding: 'utf8'});
        return files.replace(/\s/g, '\n').trim();
    },

    uncommitted: () => {
        const command = `/bin/sh ${findUncommittedFilesPath} -U`;
        const files = execSync(command, {encoding: 'utf8'});
        return files.replace(/\s/g, '\n').trim();
    },

    diff: (branch) => {
        const command = `/bin/sh ${findTagBranchDiffFilesPath} ${branch}`;
        const files = execSync(command, {encoding: 'utf8'});
        return files.replace(/\s/g, '\n').trim();
    },
};