const findFile = require('./findFile');

module.exports = (options) => {
    let files = '';
    let type = 'uncommitted';

    // tracked, untracked, uncommittet
    if (options.tracked || options.untracked || options.uncommitted) {
      if (options.tracked && options.untracked || options.uncommitted) {
        type = 'uncommitted';
      } else if (options.tracked) {
        type = 'tracked';
      } else if (options.untracked) {
        type = 'untracked';
      }
      files = `${files ? files + '\s' : ''}${findFile[type]()}`;
    }

    if (options.diff) {
      type = 'diff';
      files = `${files ? files + '\s' : ''}${findFile[type](options.diff)}`;
    }

    return files;
};