const chalk = require('chalk');
const ora = require('ora');
const exec = require('child_process').exec;
const { submitTestPath } = require('../path');

/**
 * submitTest 命令的核心实现
 * @param {Object} options - 命令参数选项
 * @param {string} options.main - 主分支名称
 * @param {string} options.test - 测试分支名称（可选）
 * @param {string} options.branch - 当前分支名称
 */
function submitTest(options) {
  const { main, test, branch } = options;

  console.log(chalk.blue(`开始执行 submitTest 操作...`));
  console.log(chalk.gray(`主分支: ${main}`));
  console.log(chalk.gray(`当前分支: ${branch}`));
  if (test) {
    console.log(chalk.gray(`测试分支: ${test}`));
  }

  const spinner = ora('正在处理提交测试操作，请稍等...\n').start();

  const submitTestCmd = `/bin/sh ${submitTestPath} ${main} ${branch} ${test}`;

  exec(submitTestCmd, (err, stdout, stderr) => {
    if (err) {
      console.log(chalk.red(`错误: 提交测试操作失败`));
      console.log(err);
      if (stderr) {
        console.log(chalk.red(stderr));
      }
      spinner.text = '提交测试操作失败';
      spinner.clear().fail();
      return;
    }

    // 显示 shell 脚本的输出
    if (stdout) {
      console.log(chalk.green(stdout.trim()));
    }

    console.log(chalk.green(`提交测试操作成功`));
    spinner.text = '成功';
    spinner.clear().succeed();
  });
}

module.exports = {
  submitTest,
};
