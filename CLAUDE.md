# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Mantas CLI** (@mantas/cli) is a comprehensive front-end development scaffold CLI tool designed for enterprise teams. It provides automation for git workflows, code generation, miniprogram CI/CD, and permission management.

## Commands and Development

### Publishing and Versioning
```bash
# Version bumping and publishing to npm
npm run publish:major    # Major version bump and publish
npm run publish:minor    # Minor version bump and publish  
npm run publish:patch    # Patch version bump and publish
```

### Testing the CLI Locally
```bash
# Install globally for testing
npm install -g .

# Or link for development
npm link

# Test commands
mantas --help
mantas get beta tag
mantas generate <moduleName> --crud
```

### No Build Process
This project doesn't require a build step - it runs directly from source using Node.js. The main entry point is `src/index.js`.

## Architecture

### Core Structure
- **Main Entry**: `src/index.js` - Commander.js-based CLI interface with all command definitions
- **Operations**: `src/operation/` - Core business logic modules
  - `new.js` - Branch/tag creation operations
  - `tagOperate.js` - Git tag management
  - `template/` - Code generation system
  - `getFiles.js` - Git file utilities
- **Tools**: `src/tools/` - Specialized tooling
  - `branch/` - Git branch management flows
  - `release/` - Release process automation
  - `tag/` - Tag creation utilities
- **Templates**: `src/templates/` - Art-template based code generation templates
  - `models/` - Model file templates (API, fetch, interface, util)
  - `pages/` - Page component templates (CRUD, normal pages)
- **Miniprogram CI**: `src/miniprogram-ci/` - WeChat miniprogram deployment automation
- **Utils**: `src/utils/` - Shared utility functions

### Template System Architecture
The code generation system is built around Art-template engine:

1. **Template Resolution**: `resolvePathAndNameInfo()` parses module names and paths
2. **File Generation Services**:
   - `createCRUDPages()` - Generates complete CRUD page sets (List, Edit, index)
   - `createModelPages()` - Generates model files (API config, fetch, interface, util)
   - `createNormalFiles()` - Generates single files based on type selection
3. **Template Utilities**: Handle folder creation, file writing, and path resolution

### Command Architecture
All commands are defined in `src/index.js` using Commander.js pattern:
- Git operations: `get/merge/checkout beta tag`, `create/delete tag`
- Branch management: `new <character> <type>` with config file support
- Code generation: `generate <moduleName>` with `-c/-m/-n` flags
- Miniprogram CI: `mini-ci` with environment-specific uploads
- Permission management: `insert sql` for database permission generation

### Key Dependencies
- **commander**: CLI framework for command parsing
- **inquirer**: Interactive command-line prompts
- **art-template**: Template engine for code generation
- **miniprogram-ci**: WeChat miniprogram deployment API
- **@mantas/request**: Internal HTTP client library
- **chalk**: Terminal text styling
- **ora**: Loading spinners for long operations

## Development Patterns

### Adding New Commands
1. Define command in `src/index.js` using Commander.js syntax
2. Create corresponding operation module in `src/operation/` or `src/tools/`
3. Add templates in `src/templates/` if code generation is involved
4. Follow existing patterns for error handling and user feedback

### Template Development
- Use Art-template syntax for dynamic content
- Templates support nested module names (e.g., `temp/tempGroup`)
- Place model templates in `src/templates/models/`
- Place page templates in `src/templates/pages/`
- Use `.art` extension for template files

### Miniprogram CI Configuration
Projects using miniprogram CI must configure:
- `tools/envs/` folder with environment-specific `.env` files
- Required env vars: `APP_ID`, `PRIVATE_KEY_PATH`, `ROBOT_NO`, `PROJECT_PATH`
- GitLab CI integration with `mantas mini-ci -un $GITLAB_USER_NAME -t $CI_COMMIT_REF_NAME -e dev`

## Important Notes

- This is a Node.js CLI tool, not a web application
- The CLI is designed for enterprise front-end teams working with React/TypeScript
- Code generation templates assume specific project structures and conventions
- Git operations are tightly integrated with specific branching workflows
- Permission SQL generation supports multiple platforms (chain, admin, supplier, b2b, crm)