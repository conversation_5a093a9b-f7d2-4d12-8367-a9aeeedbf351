[toc]

## Mantas-cli 魔鬼鱼前端脚手架

### 用前须知

**V1 版本不再维护！！！**
**目前只有 V2 版本！！！**

### 安装

```bash
# 首先确认自己的npm/yarn使用的是npm.petkit.com的源

# 使用npm
npm install -g @mantas/cli

# 使用yarn
yarn add --global @mantas/cli

# 使用pnpm
pnpm add --global @mantas/cli
```

### 使用

#### beta tag 操作相关

ver 2.2.0

```bash
# 对最新的beta tag的操作：
mantas get beta tag        # 显示最新的beta tag
mantas merge beta tag      # 当前分支合并最新的beta tag
mantas checkout beta tag   # 切换到最新的beta tag
```

#### create tag 相关

ver 2.2.0

```bash
# 打个tag
mantas create tag
```

ver 2.4.1 新增：

> 当项目为小程序时，执行以上的命令之后，界面会提示用户输入当前小程序的更新版本是什么

#### create branch 相关

ver 2.5.1

```bash
# 根据提供的项目路径，来同时创建多个相同的分支
mantas new normal branch -c ~/test.json
```

ver 2.5.0

```bash
# 切个分支 仅限于当前目录
mantas new normal branch
mantas new normal branch -n feature/test
```

#### 添加 SQL 权限相关：

ver 2.2.0

```bash
# 执行命令，会有输入的提示，按照提示提供内容即可
mantas insert sql
```

#### 查看帮助

ver 2.2.0

不知道有哪些操作，还可以使用 `-h` or `--help`命令查看

```bash
mantas --help
mantas -h
```

#### 自动生成文件和代码

ver 2.3.0

自动生成文件和代码，提高工作效率

##### CRUD 代码与文件

ver 2.3.0

```bash
# moduleName为 一个crud单位的名称，支持嵌套(temp/tempGroup)
mantas g <moduleName> -c
mantas generate <moduleName> --crud
```

##### Model 代码与文件

ver 2.3.0

此功能只支持生成 `service/api`文件夹下的接口配置文件 和 `models/<moduleName>`下的 `fetch.ts`、 `interface.ts`、 `util.ts`

不包含 `model.ts` 文件与代码的生成，如果需要请移步[单文件代码生成](#singleFileCodeGeneration)

```bash
# moduleName为 一个model单位的名称，支持嵌套(temp/tempGroup)
mantas g <moduleName> -m
mantas generate <moduleName> --model
```

##### 单文件代码生成

ver 2.3.0

单文件按照文件类型进行代码生成，目前支持的有两种类型：`page`, `model`

```bash
# fileName为 单位的名称，支持嵌套(temp/tempGroup)
mantas g <fileName> -n
mantas generate <fileName> --normal

# 执行完命令后，会有选项进行提示选择
# ? 请选择文件类型
# > page
#   model
```

#### 小程序 ci

##### 上传

> 目前只支持上传小程序代码到微信小程序后台功能

##### 流程

在 ci 上传之前，小程序相关项目，需要人工处理几件事：

1. 配置小程序的环境和上传所需的信息

   - 在项目根目录下，需要新建一个 `tools/envs`文件夹
   - 分别创建好对应环境名开头的 `.env`文件，比如 `sandbox.env`
   - 文件中包含内容：
     - APP_ID：小程序的 appid
     - PRIVATE_KEY_PATH：小程序上传私钥的存放路径
     - ROBOT_NO：上传的发布者信息，1-16 的编号可选，其它内容不可使用
     - PROJECT_PATH：编译好的项目的存放路径

2. 在配置 `gitlab-ci.yml`文件时，需要做调整：

   - 需要配置命令 `mantas mini-ci -un $GITLAB_USER_NAME -t $CI_COMMIT_REF_NAME -e dev`

### 附录

#### 添加 SQL 权限说明

ver 2.2.0

首先，确认你所要添加的权限 在 哪个端？哪个菜单下面?
然后，去数据库的 permission 表中寻找该菜单:
以 门店端 会员 一级菜单为例：
![permission表结构](http://img3.petkit.cn/img/c51b794cbf3f4cada1d746259b3a112c)
请输入添加的菜单名称： page:shop:vip:user:xxxxxxx
? 请输入所属平台： chain
? 请输入 URL： /chain/xxxx/xxxx
? 请输入排序（仅限数字）： 第几个子菜单 \* 10
? 父级权限值： page:shop:vip:user:title 如果是一级菜单，则直接回车即可
? icon 地址： 一级菜单需要提供，三级菜单直接 icon-ElegantIconsiconcircleempty

##### 目前支持的权限模块

- chain 门店端
- admin 管理端
- supplier 供应商端
- b2b 订货端
- crm 客户关系管理

#### 持续更新中......
