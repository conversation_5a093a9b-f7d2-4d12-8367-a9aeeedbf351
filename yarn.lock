# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.10.4"
  resolved "http://npm.petkit.com/@babel%2fcode-frame/-/code-frame-7.10.4.tgz#168da1a36e90da68ae8d49c0f1b48c7c6249213a"
  integrity sha1-Fo2ho26Q2miujUnA8bSMfGJJITo=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/helper-validator-identifier@^7.10.4":
  version "7.10.4"
  resolved "http://npm.petkit.com/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.10.4.tgz#a78c7a7251e01f616512d31b10adcf52ada5e0d2"
  integrity sha1-p4x6clHgH2FlEtMbEK3PUq2l4NI=

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "http://npm.petkit.com/@babel%2fhighlight/-/highlight-7.10.4.tgz#7d1bdfd65753538fabe6c38596cdb76d9ac60143"
  integrity sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@commitlint/cli@^7.2.1":
  version "7.6.1"
  resolved "http://npm.petkit.com/@commitlint%2fcli/-/cli-7.6.1.tgz#a93cf995082831999937f6d5ec1a582c8fc0393a"
  integrity sha1-qTz5lQgoMZmZN/bV7BpYLI/AOTo=
  dependencies:
    "@commitlint/format" "^7.6.1"
    "@commitlint/lint" "^7.6.0"
    "@commitlint/load" "^7.6.1"
    "@commitlint/read" "^7.6.0"
    babel-polyfill "6.26.0"
    chalk "2.3.1"
    get-stdin "7.0.0"
    lodash "4.17.11"
    meow "5.0.0"
    resolve-from "5.0.0"
    resolve-global "1.0.0"

"@commitlint/config-conventional@^7.1.2":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fconfig-conventional/-/config-conventional-7.6.0.tgz#f3dc66bf39e3b627fdd6f5ac3d0510dd0dd38f94"
  integrity sha1-89xmvznjtif91vWsPQUQ3Q3Tj5Q=

"@commitlint/ensure@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fensure/-/ensure-7.6.0.tgz#e873ff6786a3b9504e88a4debed41df29cd8ac36"
  integrity sha1-6HP/Z4ajuVBOiKTevtQd8pzYrDY=
  dependencies:
    lodash "4.17.11"

"@commitlint/execute-rule@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fexecute-rule/-/execute-rule-7.6.0.tgz#60c1c34b5f2fca6c6cbca019a9c7d81c2fab1e4a"
  integrity sha1-YMHDS18vymxsvKAZqcfYHC+rHko=
  dependencies:
    babel-runtime "6.26.0"

"@commitlint/format@^7.6.1":
  version "7.6.1"
  resolved "http://npm.petkit.com/@commitlint%2fformat/-/format-7.6.1.tgz#106750de50fab7d153eed80e7577c768bb9a3a1b"
  integrity sha1-EGdQ3lD6t9FT7tgOdXfHaLuaOhs=
  dependencies:
    babel-runtime "^6.23.0"
    chalk "^2.0.1"

"@commitlint/is-ignored@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fis-ignored/-/is-ignored-7.6.0.tgz#d069f25741dcf859b324e5f709835af3aac9cf45"
  integrity sha1-0GnyV0Hc+FmzJOX3CYNa86rJz0U=
  dependencies:
    semver "6.0.0"

"@commitlint/lint@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2flint/-/lint-7.6.0.tgz#a6da320026b937aa9bf971e060e471edd6b088ec"
  integrity sha1-ptoyACa5N6qb+XHgYORx7dawiOw=
  dependencies:
    "@commitlint/is-ignored" "^7.6.0"
    "@commitlint/parse" "^7.6.0"
    "@commitlint/rules" "^7.6.0"
    babel-runtime "^6.23.0"
    lodash "4.17.11"

"@commitlint/load@^7.6.1":
  version "7.6.2"
  resolved "http://npm.petkit.com/@commitlint%2fload/-/load-7.6.2.tgz#b5ed8163fa3117d60faf70a4e677b2017bbc71bb"
  integrity sha1-te2BY/oxF9YPr3Ck5neyAXu8cbs=
  dependencies:
    "@commitlint/execute-rule" "^7.6.0"
    "@commitlint/resolve-extends" "^7.6.0"
    babel-runtime "^6.23.0"
    cosmiconfig "^5.2.0"
    lodash "4.17.11"
    resolve-from "^5.0.0"

"@commitlint/message@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fmessage/-/message-7.6.0.tgz#899b6b411945dd091d261408b6e994043967bc06"
  integrity sha1-iZtrQRlF3QkdJhQItumUBDlnvAY=

"@commitlint/parse@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fparse/-/parse-7.6.0.tgz#e7b8d6dc145e78cf56940bbf405ca6fac3085196"
  integrity sha1-57jW3BReeM9WlAu/QFym+sMIUZY=
  dependencies:
    conventional-changelog-angular "^1.3.3"
    conventional-commits-parser "^2.1.0"
    lodash "^4.17.11"

"@commitlint/read@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fread/-/read-7.6.0.tgz#e55863354b436683daa2081de7ec2189573bc306"
  integrity sha1-5VhjNUtDZoPaoggd5+whiVc7wwY=
  dependencies:
    "@commitlint/top-level" "^7.6.0"
    "@marionebl/sander" "^0.6.0"
    babel-runtime "^6.23.0"
    git-raw-commits "^1.3.0"

"@commitlint/resolve-extends@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fresolve-extends/-/resolve-extends-7.6.0.tgz#0680b76eeb0e41f728c2f38645473a0956299edb"
  integrity sha1-BoC3busOQfcowvOGRUc6CVYpnts=
  dependencies:
    babel-runtime "6.26.0"
    import-fresh "^3.0.0"
    lodash "4.17.11"
    resolve-from "^5.0.0"
    resolve-global "^1.0.0"

"@commitlint/rules@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2frules/-/rules-7.6.0.tgz#f9a833c1eab8144fd8f545a71408b39e51acb64e"
  integrity sha1-+agzweq4FE/Y9UWnFAiznlGstk4=
  dependencies:
    "@commitlint/ensure" "^7.6.0"
    "@commitlint/message" "^7.6.0"
    "@commitlint/to-lines" "^7.6.0"
    babel-runtime "^6.23.0"

"@commitlint/to-lines@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2fto-lines/-/to-lines-7.6.0.tgz#5ed4dbf39db0ceff96dbb661b9ce048ed3db7a4b"
  integrity sha1-XtTb852wzv+W27Zhuc4EjtPbeks=

"@commitlint/top-level@^7.6.0":
  version "7.6.0"
  resolved "http://npm.petkit.com/@commitlint%2ftop-level/-/top-level-7.6.0.tgz#0ed88078ac585c93ee314ff3b7f8c20143c57652"
  integrity sha1-DtiAeKxYXJPuMU/zt/jCAUPFdlI=
  dependencies:
    find-up "^2.1.0"

"@marionebl/sander@^0.6.0":
  version "0.6.1"
  resolved "http://npm.petkit.com/@marionebl%2fsander/-/sander-0.6.1.tgz#1958965874f24bc51be48875feb50d642fc41f7b"
  integrity sha1-GViWWHTyS8Ub5Ih1/rUNZC/EH3s=
  dependencies:
    graceful-fs "^4.1.3"
    mkdirp "^0.5.1"
    rimraf "^2.5.2"

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/@types/color-name/download/@types/color-name-1.1.1.tgz#1c1261bbeaa10a8055bbc5d8ab84b7b2afc846a0"
  integrity sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "http://npm.petkit.com/@types%2fparse-json/-/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

JSONStream@^1.0.4:
  version "1.3.5"
  resolved "http://npm.petkit.com/JSONStream/-/JSONStream-1.3.5.tgz#3208c1f08d3a4d99261ab64f92302bc15e111ca0"
  integrity sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

ansi-escapes@^4.2.1:
  version "4.3.1"
  resolved "https://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-4.3.1.tgz?cache=0&sync_timestamp=1583072820951&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-escapes%2Fdownload%2Fansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://npm.petkit.com/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-4.2.1.tgz#90ae75c424d008d2624c5bf29ead3177ebfcf359"
  integrity sha1-kK51xCTQCNJiTFvynq0xd+v881k=
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://npm.petkit.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://npm.petkit.com/array-find-index/-/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-ify@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/array-ify/-/array-ify-1.0.0.tgz#9e528762b4a9066ad163a6962a364418e9626ece"
  integrity sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.petkit.com/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

babel-polyfill@6.26.0:
  version "6.26.0"
  resolved "http://npm.petkit.com/babel-polyfill/-/babel-polyfill-6.26.0.tgz#379937abc67d7895970adc621f284cd966cf2153"
  integrity sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=
  dependencies:
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    regenerator-runtime "^0.10.5"

babel-runtime@6.26.0, babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://npm.petkit.com/babel-runtime/-/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://npm.petkit.com/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/caller-callsite/-/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/caller-path/-/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/callsites/-/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://npm.petkit.com/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-keys@^4.0.0:
  version "4.2.0"
  resolved "http://npm.petkit.com/camelcase-keys/-/camelcase-keys-4.2.0.tgz#a2aa5fb1af688758259c32c141426d78923b9b77"
  integrity sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=
  dependencies:
    camelcase "^4.1.0"
    map-obj "^2.0.0"
    quick-lru "^1.0.0"

camelcase@^4.1.0:
  version "4.1.0"
  resolved "http://npm.petkit.com/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

chalk@2.3.1:
  version "2.3.1"
  resolved "http://npm.petkit.com/chalk/-/chalk-2.3.1.tgz#523fe2678aec7b04e8041909292fe8b17059b796"
  integrity sha1-Uj/iZ4rsewToBBkJKS/osXBZt5Y=
  dependencies:
    ansi-styles "^3.2.0"
    escape-string-regexp "^1.0.5"
    supports-color "^5.2.0"

chalk@^2.0.0, chalk@^2.0.1:
  version "2.4.2"
  resolved "http://npm.petkit.com/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.0"
  resolved "http://npm.petkit.com/chalk/-/chalk-4.1.0.tgz#4e14870a618d9e2edd97dd8345fd9d9dc315646a"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1594010614226&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/ci-info/-/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.4.0:
  version "2.4.0"
  resolved "http://npm.petkit.com/cli-spinners/-/cli-spinners-2.4.0.tgz#c6256db216b878cfba4720e719cec7cf72685d7f"
  integrity sha1-xiVtsha4eM+6RyDnGc7Hz3JoXX8=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/cli-width/download/cli-width-3.0.0.tgz?cache=0&sync_timestamp=1586877902436&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-width%2Fdownload%2Fcli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

clone@^1.0.2:
  version "1.0.4"
  resolved "http://npm.petkit.com/clone/-/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://npm.petkit.com/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://npm.petkit.com/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

commander@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/commander/download/commander-6.0.0.tgz?cache=0&sync_timestamp=1595168248090&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-6.0.0.tgz#2b270da94f8fb9014455312f829a1129dbf8887e"
  integrity sha1-KycNqU+PuQFEVTEvgpoRKdv4iH4=

compare-func@^1.3.1:
  version "1.3.4"
  resolved "http://npm.petkit.com/compare-func/-/compare-func-1.3.4.tgz#6b07c4c5e8341119baf44578085bda0f4a823516"
  integrity sha1-awfExeg0ERm69EV4CFvaD0qCNRY=
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^3.0.0"

compare-versions@^3.6.0:
  version "3.6.0"
  resolved "http://npm.petkit.com/compare-versions/-/compare-versions-3.6.0.tgz#1a5689913685e5a87637b8d3ffca75514ec41d62"
  integrity sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://npm.petkit.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

conventional-changelog-angular@^1.3.3:
  version "1.6.6"
  resolved "http://npm.petkit.com/conventional-changelog-angular/-/conventional-changelog-angular-1.6.6.tgz#b27f2b315c16d0a1f23eb181309d0e6a4698ea0f"
  integrity sha1-sn8rMVwW0KHyPrGBMJ0OakaY6g8=
  dependencies:
    compare-func "^1.3.1"
    q "^1.5.1"

conventional-commits-parser@^2.1.0:
  version "2.1.7"
  resolved "http://npm.petkit.com/conventional-commits-parser/-/conventional-commits-parser-2.1.7.tgz#eca45ed6140d72ba9722ee4132674d639e644e8e"
  integrity sha1-7KRe1hQNcrqXIu5BMmdNY55kTo4=
  dependencies:
    JSONStream "^1.0.4"
    is-text-path "^1.0.0"
    lodash "^4.2.1"
    meow "^4.0.0"
    split2 "^2.0.0"
    through2 "^2.0.0"
    trim-off-newlines "^1.0.0"

core-js@^2.4.0, core-js@^2.5.0:
  version "2.6.11"
  resolved "http://npm.petkit.com/core-js/-/core-js-2.6.11.tgz#38831469f9922bded8ee21c9dc46985e0399308c"
  integrity sha1-OIMUafmSK97Y7iHJ3EaYXgOZMIw=

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://npm.petkit.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.2.0:
  version "5.2.1"
  resolved "http://npm.petkit.com/cosmiconfig/-/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "http://npm.petkit.com/cosmiconfig/-/cosmiconfig-6.0.0.tgz#da4fee853c52f6b1e6935f41c1a2fc50bd4a9982"
  integrity sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://npm.petkit.com/currently-unhandled/-/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

dargs@^4.0.1:
  version "4.1.0"
  resolved "http://npm.petkit.com/dargs/-/dargs-4.1.0.tgz#03a9dbb4b5c2f139bf14ae53f0b8a2a6a86f4e17"
  integrity sha1-A6nbtLXC8Tm/FK5T8LiipqhvThc=
  dependencies:
    number-is-nan "^1.0.0"

decamelize-keys@^1.0.0:
  version "1.1.0"
  resolved "http://npm.petkit.com/decamelize-keys/-/decamelize-keys-1.1.0.tgz#d171a87933252807eb3cb61dc1c1445d078df2d9"
  integrity sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0:
  version "1.2.0"
  resolved "http://npm.petkit.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://npm.petkit.com/defaults/-/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

dot-prop@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/dot-prop/-/dot-prop-3.0.0.tgz#1b708af094a49c9a0e7dbcad790aba539dac1177"
  integrity sha1-G3CK8JSknJoOfbyteQq6U52sEXc=
  dependencies:
    is-obj "^1.0.0"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://npm.petkit.com/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://npm.petkit.com/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/figures/download/figures-3.2.0.tgz?cache=0&sync_timestamp=1581893111153&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffigures%2Fdownload%2Ffigures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "http://npm.petkit.com/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "http://npm.petkit.com/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-versions@^3.2.0:
  version "3.2.0"
  resolved "http://npm.petkit.com/find-versions/-/find-versions-3.2.0.tgz#10297f98030a786829681690545ef659ed1d254e"
  integrity sha1-ECl/mAMKeGgpaBaQVF72We0dJU4=
  dependencies:
    semver-regex "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

get-stdin@7.0.0:
  version "7.0.0"
  resolved "http://npm.petkit.com/get-stdin/-/get-stdin-7.0.0.tgz#8d5de98f15171a125c5e516643c7a6d0ea8a96f6"
  integrity sha1-jV3pjxUXGhJcXlFmQ8em0OqKlvY=

git-raw-commits@^1.3.0:
  version "1.3.6"
  resolved "http://npm.petkit.com/git-raw-commits/-/git-raw-commits-1.3.6.tgz#27c35a32a67777c1ecd412a239a6c19d71b95aff"
  integrity sha1-J8NaMqZ3d8Hs1BKiOabBnXG5Wv8=
  dependencies:
    dargs "^4.0.1"
    lodash.template "^4.0.2"
    meow "^4.0.0"
    split2 "^2.0.0"
    through2 "^2.0.0"

glob@^7.1.3:
  version "7.1.6"
  resolved "http://npm.petkit.com/glob/-/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.1:
  version "0.1.1"
  resolved "http://npm.petkit.com/global-dirs/-/global-dirs-0.1.1.tgz#b319c0dd4607f353f3be9cca4c72fc148c49f445"
  integrity sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=
  dependencies:
    ini "^1.3.4"

graceful-fs@^4.1.2, graceful-fs@^4.1.3:
  version "4.2.4"
  resolved "http://npm.petkit.com/graceful-fs/-/graceful-fs-4.2.4.tgz#2256bde14d3632958c465ebc96dc467ca07a29fb"
  integrity sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "http://npm.petkit.com/hosted-git-info/-/hosted-git-info-2.8.8.tgz#7539bd4bc1e0e0a895815a2e0262420b12858488"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

husky@^4.2.5:
  version "4.2.5"
  resolved "http://npm.petkit.com/husky/-/husky-4.2.5.tgz#2b4f7622673a71579f901d9885ed448394b5fa36"
  integrity sha1-K092Imc6cVefkB2Yhe1Eg5S1+jY=
  dependencies:
    chalk "^4.0.0"
    ci-info "^2.0.0"
    compare-versions "^3.6.0"
    cosmiconfig "^6.0.0"
    find-versions "^3.2.0"
    opencollective-postinstall "^2.0.2"
    pkg-dir "^4.2.0"
    please-upgrade-node "^3.2.0"
    slash "^3.0.0"
    which-pm-runs "^1.0.0"

iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1594184224398&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/import-fresh/-/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.1.0:
  version "3.2.1"
  resolved "http://npm.petkit.com/import-fresh/-/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
  integrity sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://npm.petkit.com/indent-string/-/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://npm.petkit.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://npm.petkit.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ini@^1.3.4:
  version "1.3.5"
  resolved "http://npm.petkit.com/ini/-/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"
  integrity sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=

inquirer@^7.3.3:
  version "7.3.3"
  resolved "https://registry.npm.taobao.org/inquirer/download/inquirer-7.3.3.tgz?cache=0&sync_timestamp=1595471640963&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finquirer%2Fdownload%2Finquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://npm.petkit.com/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://npm.petkit.com/is-directory/-/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/is-interactive/-/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-obj@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/is-obj/-/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://npm.petkit.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-text-path@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/is-text-path/-/is-text-path-1.0.1.tgz#4e1aa0fb51bfbcb3e92688001397202c1775b66e"
  integrity sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=
  dependencies:
    text-extensions "^1.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.0"
  resolved "http://npm.petkit.com/js-yaml/-/js-yaml-3.14.0.tgz#a7a34170f26a21bb162424d8adacb4113a69e482"
  integrity sha1-p6NBcPJqIbsWJCTYray0ETpp5II=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://npm.petkit.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "http://npm.petkit.com/jsonparse/-/jsonparse-1.3.1.tgz#3f4dae4a91fac315f71062f8521cc239f1366280"
  integrity sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "http://npm.petkit.com/lines-and-columns/-/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/load-json-file/-/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://npm.petkit.com/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"
  integrity sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=

lodash.template@^4.0.2:
  version "4.5.0"
  resolved "http://npm.petkit.com/lodash.template/-/lodash.template-4.5.0.tgz#f976195cf3f347d0d5f52483569fe8031ccce8ab"
  integrity sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "http://npm.petkit.com/lodash.templatesettings/-/lodash.templatesettings-4.2.0.tgz#e481310f049d3cf6d47e912ad09313b154f0fb33"
  integrity sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash@4.17.11:
  version "4.17.11"
  resolved "http://npm.petkit.com/lodash/-/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"
  integrity sha1-s56mIp72B+zYniyN8SU2iRysm40=

lodash@^4.17.11, lodash@^4.17.19, lodash@^4.2.1:
  version "4.17.19"
  resolved "https://registry.npm.taobao.org/lodash/download/lodash-4.17.19.tgz?cache=0&sync_timestamp=1594256727883&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash%2Fdownload%2Flodash-4.17.19.tgz#e48ddedbe30b3321783c5b4301fbd353bc1e4a4b"
  integrity sha1-5I3e2+MLMyF4PFtDAfvTU7weSks=

log-symbols@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/log-symbols/-/log-symbols-4.0.0.tgz#69b3cc46d20f448eccdb75ea1fa733d9e821c920"
  integrity sha1-abPMRtIPRI7M23XqH6cz2eghySA=
  dependencies:
    chalk "^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://npm.petkit.com/loud-rejection/-/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

map-obj@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/map-obj/-/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/map-obj/-/map-obj-2.0.0.tgz#a65cd29087a92598b8791257a523e021222ac1f9"
  integrity sha1-plzSkIepJZi4eRJXpSPgISIqwfk=

meow@5.0.0:
  version "5.0.0"
  resolved "http://npm.petkit.com/meow/-/meow-5.0.0.tgz#dfc73d63a9afc714a5e371760eb5c88b91078aa4"
  integrity sha1-38c9Y6mvxxSl43F2DrXIi5EHiqQ=
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"
    yargs-parser "^10.0.0"

meow@^4.0.0:
  version "4.0.1"
  resolved "http://npm.petkit.com/meow/-/meow-4.0.1.tgz#d48598f6f4b1472f35bf6317a95945ace347f975"
  integrity sha1-1IWY9vSxRy81v2MXqVlFrONH+XU=
  dependencies:
    camelcase-keys "^4.0.0"
    decamelize-keys "^1.0.0"
    loud-rejection "^1.0.0"
    minimist "^1.1.3"
    minimist-options "^3.0.1"
    normalize-package-data "^2.3.4"
    read-pkg-up "^3.0.0"
    redent "^2.0.0"
    trim-newlines "^2.0.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.1.0.tgz?cache=0&sync_timestamp=1596093969209&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmimic-fn%2Fdownload%2Fmimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://npm.petkit.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
  dependencies:
    brace-expansion "^1.1.7"

minimist-options@^3.0.1:
  version "3.0.2"
  resolved "http://npm.petkit.com/minimist-options/-/minimist-options-3.0.2.tgz#fba4c8191339e13ecf4d61beb03f070103f3d954"
  integrity sha1-+6TIGRM54T7PTWG+sD8HAQPz2VQ=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"

minimist@^1.1.3, minimist@^1.2.5:
  version "1.2.5"
  resolved "http://npm.petkit.com/minimist/-/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mkdirp@^0.5.1:
  version "0.5.5"
  resolved "http://npm.petkit.com/mkdirp/-/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.5.0"
  resolved "http://npm.petkit.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

once@^1.3.0:
  version "1.4.0"
  resolved "http://npm.petkit.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/onetime/download/onetime-5.1.1.tgz?cache=0&sync_timestamp=1596455434601&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fonetime%2Fdownload%2Fonetime-5.1.1.tgz#5c8016847b0d67fcedb7eef254751cfcdc7e9418"
  integrity sha1-XIAWhHsNZ/ztt+7yVHUc/Nx+lBg=
  dependencies:
    mimic-fn "^2.1.0"

opencollective-postinstall@^2.0.2:
  version "2.0.3"
  resolved "http://npm.petkit.com/opencollective-postinstall/-/opencollective-postinstall-2.0.3.tgz#7a0fff978f6dbfa4d006238fbac98ed4198c3259"
  integrity sha1-eg//l49tv6TQBiOPusmO1BmMMlk=

ora@^5.0.0:
  version "5.0.0"
  resolved "http://npm.petkit.com/ora/-/ora-5.0.0.tgz#4f0b34f2994877b49b452a707245ab1e9f6afccb"
  integrity sha1-Tws08plId7SbRSpwckWrHp9q/Ms=
  dependencies:
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.4.0"
    is-interactive "^1.0.0"
    log-symbols "^4.0.0"
    mute-stream "0.0.8"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://npm.petkit.com/p-limit/-/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://npm.petkit.com/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://npm.petkit.com/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/p-try/-/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://npm.petkit.com/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/parse-json/-/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.0.1"
  resolved "http://npm.petkit.com/parse-json/-/parse-json-5.0.1.tgz#7cfe35c1ccd641bce3981467e6c2ece61b3b3878"
  integrity sha1-fP41wczWQbzjmBRn5sLs5hs7OHg=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"
    lines-and-columns "^1.1.6"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://npm.petkit.com/path-parse/-/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/path-type/-/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/pify/-/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://npm.petkit.com/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

please-upgrade-node@^3.2.0:
  version "3.2.0"
  resolved "http://npm.petkit.com/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://npm.petkit.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

q@^1.5.1:
  version "1.5.1"
  resolved "http://npm.petkit.com/q/-/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

quick-lru@^1.0.0:
  version "1.1.0"
  resolved "http://npm.petkit.com/quick-lru/-/quick-lru-1.1.0.tgz#4360b17c61136ad38078397ff11416e186dcfbb8"
  integrity sha1-Q2CxfGETatOAeDl/8RQW4Ybc+7g=

read-pkg-up@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/read-pkg-up/-/read-pkg-up-3.0.0.tgz#3ed496685dba0f8fe118d0691dc51f4a1ff96f07"
  integrity sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^3.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/read-pkg/-/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

readable-stream@~2.3.6:
  version "2.3.7"
  resolved "http://npm.petkit.com/readable-stream/-/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

redent@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/redent/-/redent-2.0.0.tgz#c1b2007b42d57eb1389079b3c8333639d5e1ccaa"
  integrity sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=
  dependencies:
    indent-string "^3.0.0"
    strip-indent "^2.0.0"

regenerator-runtime@^0.10.5:
  version "0.10.5"
  resolved "http://npm.petkit.com/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"
  integrity sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://npm.petkit.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==

resolve-from@5.0.0, resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://npm.petkit.com/resolve-from/-/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/resolve-from/-/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://npm.petkit.com/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-global@1.0.0, resolve-global@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/resolve-global/-/resolve-global-1.0.0.tgz#a2a79df4af2ca3f49bf77ef9ddacd322dad19255"
  integrity sha1-oqed9K8so/Sb93753azTItrRklU=
  dependencies:
    global-dirs "^0.1.1"

resolve@^1.10.0:
  version "1.17.0"
  resolved "http://npm.petkit.com/resolve/-/resolve-1.17.0.tgz#b25941b54968231cc2d1bb76a79cb7f2c0bf8444"
  integrity sha1-sllBtUloIxzC0bt2p5y38sC/hEQ=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

rimraf@^2.5.2:
  version "2.7.1"
  resolved "http://npm.petkit.com/rimraf/-/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npm.taobao.org/run-async/download/run-async-2.4.1.tgz?cache=0&sync_timestamp=1587993318918&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frun-async%2Fdownload%2Frun-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

rxjs@^6.6.0:
  version "6.6.2"
  resolved "https://registry.npm.taobao.org/rxjs/download/rxjs-6.6.2.tgz#8096a7ac03f2cc4fe5860ef6e572810d9e01c0d2"
  integrity sha1-gJanrAPyzE/lhg725XKBDZ4BwNI=
  dependencies:
    tslib "^1.9.0"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://npm.petkit.com/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/semver-compare/-/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

semver-regex@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/semver-regex/-/semver-regex-2.0.0.tgz#a93c2c5844539a770233379107b38c7b4ac9d338"
  integrity sha1-qTwsWERTmncCMzeRB7OMe0rJ0zg=

"semver@2 || 3 || 4 || 5":
  version "5.7.1"
  resolved "http://npm.petkit.com/semver/-/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@6.0.0:
  version "6.0.0"
  resolved "http://npm.petkit.com/semver/-/semver-6.0.0.tgz#05e359ee571e5ad7ed641a6eec1e547ba52dea65"
  integrity sha1-BeNZ7lceWtftZBpu7B5Ue6Ut6mU=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.3.tgz#a1410c2edd8f077b08b4e253c8eacfcaf057461c"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "http://npm.petkit.com/spdx-correct/-/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://npm.petkit.com/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://npm.petkit.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "http://npm.petkit.com/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
  integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=

split2@^2.0.0:
  version "2.2.0"
  resolved "http://npm.petkit.com/split2/-/split2-2.2.0.tgz#186b2575bcf83e85b7d18465756238ee4ee42493"
  integrity sha1-GGsldbz4PoW30YRldWI47k7kJJM=
  dependencies:
    through2 "^2.0.2"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://npm.petkit.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

string-width@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://npm.petkit.com/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&sync_timestamp=1573280577145&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://npm.petkit.com/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/strip-indent/-/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

supports-color@^5.2.0, supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://npm.petkit.com/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-7.1.0.tgz#68e32591df73e25ad1c4b49108a2ec507962bfd1"
  integrity sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E=
  dependencies:
    has-flag "^4.0.0"

text-extensions@^1.0.0:
  version "1.9.0"
  resolved "http://npm.petkit.com/text-extensions/-/text-extensions-1.9.0.tgz#1853e45fee39c945ce6f6c36b2d659b5aabc2a26"
  integrity sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=

through2@^2.0.0, through2@^2.0.2:
  version "2.0.5"
  resolved "http://npm.petkit.com/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

"through@>=2.2.7 <3", through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

trim-newlines@^2.0.0:
  version "2.0.0"
  resolved "http://npm.petkit.com/trim-newlines/-/trim-newlines-2.0.0.tgz#b403d0b91be50c331dfc4b82eeceb22c3de16d20"
  integrity sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=

trim-off-newlines@^1.0.0:
  version "1.0.1"
  resolved "http://npm.petkit.com/trim-off-newlines/-/trim-off-newlines-1.0.1.tgz#9f9ba9d9efa8764c387698bcbfeb2c848f11adb3"
  integrity sha1-n5up2e+odkw4dpi8v+sshI8RrbM=

tslib@^1.9.0:
  version "1.13.0"
  resolved "https://registry.npm.taobao.org/tslib/download/tslib-1.13.0.tgz?cache=0&sync_timestamp=1596755286708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftslib%2Fdownload%2Ftslib-1.13.0.tgz#c881e13cc7015894ed914862d276436fa9a47043"
  integrity sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM=

type-fest@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npm.taobao.org/type-fest/download/type-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://npm.petkit.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://npm.petkit.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://npm.petkit.com/wcwidth/-/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

which-pm-runs@^1.0.0:
  version "1.0.0"
  resolved "http://npm.petkit.com/which-pm-runs/-/which-pm-runs-1.0.0.tgz#670b3afbc552e0b55df6b7780ca74615f23ad1cb"
  integrity sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs=

wrappy@1:
  version "1.0.2"
  resolved "http://npm.petkit.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

xtend@~4.0.1:
  version "4.0.2"
  resolved "http://npm.petkit.com/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

yaml@^1.7.2:
  version "1.10.0"
  resolved "http://npm.petkit.com/yaml/-/yaml-1.10.0.tgz#3b593add944876077d4d683fee01081bd9fff31e"
  integrity sha1-O1k63ZRIdgd9TWg/7gEIG9n/8x4=

yargs-parser@^10.0.0:
  version "10.1.0"
  resolved "http://npm.petkit.com/yargs-parser/-/yargs-parser-10.1.0.tgz#7202265b89f7e9e9f2e5765e0fe735a905edbaa8"
  integrity sha1-cgImW4n36eny5XZeD+c1qQXtuqg=
  dependencies:
    camelcase "^4.1.0"
